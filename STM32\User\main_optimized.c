#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "nvic.h"
#include "usart.h"

// 步进电机控制参数
#define DEAD_ZONE_THRESHOLD 1.0f    // 死区阈值
#define MAX_SPEED_PAN 15            // 水平轴最大速度
#define MAX_SPEED_TILT 12           // 垂直轴最大速度
#define MIN_SPEED 3                 // 最小速度
#define SMOOTH_FACTOR 0.7f          // 平滑因子 (0-1)
#define MIN_COMMAND_INTERVAL 50     // 最小指令间隔(ms)

// 步进电机状态结构体
typedef struct {
    float current_command;          // 当前指令值
    float target_command;           // 目标指令值
    float last_command;             // 上次指令值
    uint32_t last_update_time;      // 上次更新时间
    uint8_t is_moving;              // 是否正在移动
} MotorState_t;

// 全局变量
MotorState_t pan_motor = {0};
MotorState_t tilt_motor = {0};
volatile uint32_t system_tick = 0;

// 函数声明
void SystemTick_Init(void);
float smooth_command(float current, float target, float factor);
uint16_t calculate_speed(float command_diff);
void update_motor_state(MotorState_t* motor, float new_command);
void control_pan_motor(MotorState_t* motor);
void control_tilt_motor(MotorState_t* motor);

int main(void)
{
    USART_INIT(); // 初始化串口
    NVIC_INIT();  // 配置NVIC优先级分组及使能串口接收中断
    SystemTick_Init(); // 初始化系统滴答定时器
    
    float pan_raw = 0;     // 云台水平角度（来自上位机）
    float tilt_raw = 0;    // 云台垂直角度（来自上位机）
    
    while (1)
    {    
        // 数据转换和处理 - 改进转换比例
        pan_raw = (float)(pan - 1000) / 10.0f;   
        tilt_raw = (float)(tilt - 1000) / 10.0f; 
        
        // 更新电机状态
        if(stepmotorx_Flag) // 检测到水平轴控制标志
        {
            update_motor_state(&pan_motor, pan_raw);
            control_pan_motor(&pan_motor);
            stepmotorx_Flag = 0; // 清除标志
        }
        
        if(stepmotory_Flag) // 检测到垂直轴控制标志
        {
            update_motor_state(&tilt_motor, tilt_raw);
            control_tilt_motor(&tilt_motor);
            stepmotory_Flag = 0; // 清除标志
        }
        
        Delay_ms(1); // 1ms延时，提供基本的时间基准
    }
}

// 初始化系统滴答定时器
void SystemTick_Init(void)
{
    // 使用SysTick定时器提供时间基准
    SysTick_Config(SystemCoreClock / 1000); // 1ms中断
}

// 平滑指令处理
float smooth_command(float current, float target, float factor)
{
    return current * factor + target * (1.0f - factor);
}

// 根据指令差值计算速度
uint16_t calculate_speed(float command_diff)
{
    float abs_diff = command_diff < 0 ? -command_diff : command_diff;
    
    if (abs_diff < DEAD_ZONE_THRESHOLD) {
        return 0; // 在死区内，速度为0
    }
    
    // 根据指令差值计算速度（线性映射）
    uint16_t speed = (uint16_t)(MIN_SPEED + abs_diff * 1.5);
    
    return speed;
}

// 更新电机状态
void update_motor_state(MotorState_t* motor, float new_command)
{
    motor->target_command = new_command;
    motor->current_command = smooth_command(motor->current_command, 
                                          motor->target_command, 
                                          SMOOTH_FACTOR);
    motor->last_update_time = system_tick;
}

// 控制水平轴电机
void control_pan_motor(MotorState_t* motor)
{
    float command_diff = motor->current_command - motor->last_command;
    float abs_diff = command_diff < 0 ? -command_diff : command_diff;
    
    // 死区处理
    if (abs_diff < DEAD_ZONE_THRESHOLD) {
        motor->is_moving = 0;
        return;
    }
    
    // 频率控制 - 避免发送过于频繁的指令
    if (system_tick - motor->last_update_time < MIN_COMMAND_INTERVAL) {
        return;
    }
    
    motor->is_moving = 1;
    motor->last_command = motor->current_command;
    
    // 计算速度
    uint16_t speed = calculate_speed(abs_diff);
    if (speed > MAX_SPEED_PAN) speed = MAX_SPEED_PAN;
    if (speed < MIN_SPEED) speed = MIN_SPEED;
    
    // 确定方向和角度
    uint8_t direction = (command_diff > 0) ? 1 : 0; // 1=右转, 0=左转
    uint16_t angle = (uint16_t)(abs_diff + 0.5f); // 四舍五入
    
    // 角度限制
    if (angle > 180) angle = 180;
    if (angle < 1) angle = 1;
    
    // 填充并发送数据包
    StepperPacket_Fill(&stepper_packet_2, 0x01, 0x02, direction, 0x20, angle, speed);
    USART2_SendStepperPacket(&stepper_packet_2);
}

// 控制垂直轴电机
void control_tilt_motor(MotorState_t* motor)
{
    float command_diff = motor->current_command - motor->last_command;
    float abs_diff = command_diff < 0 ? -command_diff : command_diff;
    
    // 死区处理
    if (abs_diff < DEAD_ZONE_THRESHOLD) {
        motor->is_moving = 0;
        return;
    }
    
    // 频率控制 - 避免发送过于频繁的指令
    if (system_tick - motor->last_update_time < MIN_COMMAND_INTERVAL) {
        return;
    }
    
    motor->is_moving = 1;
    motor->last_command = motor->current_command;
    
    // 计算速度
    uint16_t speed = calculate_speed(abs_diff);
    if (speed > MAX_SPEED_TILT) speed = MAX_SPEED_TILT;
    if (speed < MIN_SPEED) speed = MIN_SPEED;
    
    // 确定方向和角度
    uint8_t direction = (command_diff > 0) ? 1 : 0; // 1=上转, 0=下转
    uint16_t angle = (uint16_t)(abs_diff + 0.5f); // 四舍五入
    
    // 角度限制
    if (angle > 180) angle = 180;
    if (angle < 1) angle = 1;
    
    // 填充并发送数据包
    StepperPacket_Fill(&stepper_packet_3, 0x02, 0x02, direction, 0x20, angle, speed);
    USART3_SendStepperPacket(&stepper_packet_3);
}

// SysTick中断处理函数
void SysTick_Handler(void)
{
    system_tick++; // 系统滴答计数器递增
}
