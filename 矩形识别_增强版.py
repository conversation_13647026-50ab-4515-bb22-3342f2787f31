import time, os, sys
import math
import cv_lite
import ulab.numpy as np
from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms
from machine import UART, FPIOA

# 导入配置文件
try:
    from config import *
    print("配置文件加载成功")
except ImportError:
    print("未找到config.py，使用默认参数")
    PID_X_KP, PID_X_KI, PID_X_KD = 3.0, 0.1, 1.0
    PID_Y_KP, PID_Y_KI, PID_Y_KD = 3.0, 0.1, 1.0
    TARGET_MAX_HISTORY, TARGET_STABILITY_THRESHOLD = 5, 5
    GIMBAL_DEAD_ZONE, GIMBAL_MIN_SEND_INTERVAL = 1.5, 80

# 1. 摄像头初始化
sensor.reset()
sensor.set_framesize(sensor.QVGA)  # 320x240
sensor.set_pixformat(sensor.RGB565)
sensor.run(1)

# 2. 显示器初始化
Display.init(Display.ST7701, width=800, height=480, to_ide=True)
lcd_width = Display.width()
lcd_height = Display.height()

# 3. 媒体管理器初始化
MediaManager.init()

# 4. 串口初始化
fpioa = FPIOA()
fpioa.set_function(10, FPIOA.UART2_TX)
fpioa.set_function(9, FPIOA.UART2_RX)
uart2 = UART(UART.UART2, 115200, 8, 1, 0, timeout=1000, read_buf_len=4096)

# 5. 增强的矩形检测参数
RECT_WIDTH = 210
RECT_HEIGHT = 95
BORDER_TOLERANCE = 8

# 矩形识别增强参数
MIN_RECT_AREA = 800          # 最小矩形面积
MAX_RECT_AREA = 50000        # 最大矩形面积
MIN_ASPECT_RATIO = 1.5       # 最小宽高比（矩形应该是长方形）
MAX_ASPECT_RATIO = 4.0       # 最大宽高比
MIN_EDGE_LENGTH = 20         # 最小边长
ANGLE_TOLERANCE = 15         # 角度容忍度（度）
CORNER_ANGLE_THRESHOLD = 70  # 角点角度阈值（度）

# 6. 高精度PID控制器
class ImprovedPID:
    def __init__(self, kp=0.8, ki=0.02, kd=0.15, max_output=30, integral_limit=20):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.max_output = max_output
        self.integral_limit = integral_limit
        self.prev_error = 0
        self.integral = 0
        self.prev_time = ticks_ms()
        self.error_history = []
        self.max_history = 3

    def compute(self, error):
        current_time = ticks_ms()
        dt = (current_time - self.prev_time) / 1000.0
        if dt <= 0: 
            dt = 0.01
        
        self.error_history.append(error)
        if len(self.error_history) > self.max_history:
            self.error_history.pop(0)
        filtered_error = sum(self.error_history) / len(self.error_history)

        self.integral += filtered_error * dt
        if self.integral > self.integral_limit: 
            self.integral = self.integral_limit
        elif self.integral < -self.integral_limit: 
            self.integral = -self.integral_limit

        derivative = (filtered_error - self.prev_error) / dt
        output = self.kp * filtered_error + self.ki * self.integral + self.kd * derivative

        if output > self.max_output: 
            output = self.max_output
        elif output < -self.max_output: 
            output = -self.max_output

        self.prev_error = filtered_error
        self.prev_time = current_time
        return output

    def reset(self):
        self.prev_error = 0
        self.integral = 0
        self.error_history = []

# 创建PID控制器
pid_x = ImprovedPID(kp=PID_X_KP, ki=max(PID_X_KI, 0.15), kd=PID_X_KD, max_output=25, integral_limit=25)
pid_y = ImprovedPID(kp=PID_Y_KP, ki=max(PID_Y_KI, 0.12), kd=PID_Y_KD, max_output=20, integral_limit=20)

# 7. 增强的矩形验证器
class EnhancedRectangleValidator:
    def __init__(self):
        self.history_rectangles = []
        self.max_history = 5
        
    def calculate_angle(self, p1, p2, p3):
        """计算三点构成的角度"""
        try:
            v1 = (p1[0] - p2[0], p1[1] - p2[1])
            v2 = (p3[0] - p2[0], p3[1] - p2[1])
            
            dot_product = v1[0] * v2[0] + v1[1] * v2[1]
            mag1 = math.sqrt(v1[0]**2 + v1[1]**2)
            mag2 = math.sqrt(v2[0]**2 + v2[1]**2)
            
            if mag1 == 0 or mag2 == 0:
                return 0
                
            cos_angle = dot_product / (mag1 * mag2)
            cos_angle = max(-1, min(1, cos_angle))  # 限制在[-1, 1]范围内
            angle = math.acos(cos_angle) * 180 / math.pi
            
            return angle
        except:
            return 0
    
    def is_valid_rectangle(self, corners):
        """增强的矩形验证"""
        if len(corners) != 4:
            return False, 0
            
        try:
            # 1. 计算边长
            edge_lengths = []
            for i in range(4):
                x1, y1 = corners[i]
                x2, y2 = corners[(i+1) % 4]
                length = math.sqrt((x2-x1)**2 + (y2-y1)**2)
                edge_lengths.append(length)
            
            # 检查最小边长
            if min(edge_lengths) < MIN_EDGE_LENGTH:
                return False, 0
            
            # 2. 检查对边长度相似性
            edge_lengths.sort()
            # 对于矩形，应该有两对相似的边
            ratio1 = edge_lengths[1] / max(edge_lengths[0], 1)
            ratio2 = edge_lengths[3] / max(edge_lengths[2], 1)
            
            if ratio1 < 0.7 or ratio2 < 0.7:  # 提高相似性要求
                return False, 0
            
            # 3. 检查角度（矩形的角应该接近90度）
            angles = []
            for i in range(4):
                p1 = corners[(i-1) % 4]
                p2 = corners[i]
                p3 = corners[(i+1) % 4]
                angle = self.calculate_angle(p1, p2, p3)
                angles.append(angle)
            
            # 检查角度是否接近90度
            right_angle_count = 0
            for angle in angles:
                if abs(angle - 90) < ANGLE_TOLERANCE:
                    right_angle_count += 1
            
            if right_angle_count < 3:  # 至少3个角接近90度
                return False, 0
            
            # 4. 计算面积和宽高比
            # 使用鞋带公式计算面积
            area = 0
            for i in range(4):
                j = (i + 1) % 4
                area += corners[i][0] * corners[j][1]
                area -= corners[j][0] * corners[i][1]
            area = abs(area) / 2
            
            if area < MIN_RECT_AREA or area > MAX_RECT_AREA:
                return False, 0
            
            # 5. 检查宽高比
            # 找到最长和最短的边作为长和宽
            max_edge = max(edge_lengths)
            min_edge = min(edge_lengths)
            aspect_ratio = max_edge / max(min_edge, 1)
            
            if aspect_ratio < MIN_ASPECT_RATIO or aspect_ratio > MAX_ASPECT_RATIO:
                return False, 0
            
            # 6. 计算置信度分数
            confidence = 0
            confidence += min(ratio1, ratio2) * 30  # 边长相似性 (0-30分)
            confidence += (right_angle_count / 4) * 40  # 角度正确性 (0-40分)
            confidence += min(area / MIN_RECT_AREA, 5) * 6  # 面积合理性 (0-30分)
            
            return True, min(confidence, 100)
            
        except Exception as e:
            print("矩形验证错误: " + str(e))
            return False, 0
    
    def add_to_history(self, rect_info):
        """添加到历史记录"""
        self.history_rectangles.append(rect_info)
        if len(self.history_rectangles) > self.max_history:
            self.history_rectangles.pop(0)
    
    def get_best_rectangle(self, candidates):
        """从候选矩形中选择最佳的"""
        if not candidates:
            return None
            
        # 按置信度排序
        candidates.sort(key=lambda x: x['confidence'], reverse=True)
        
        # 如果有历史记录，优先选择与历史位置接近的矩形
        if self.history_rectangles and len(candidates) > 1:
            last_center = self.history_rectangles[-1]['center']
            
            for candidate in candidates:
                center = candidate['center']
                distance = math.sqrt((center[0] - last_center[0])**2 + (center[1] - last_center[1])**2)
                
                # 如果距离很近且置信度足够，优先选择
                if distance < 50 and candidate['confidence'] > 60:
                    return candidate
        
        # 否则选择置信度最高的
        return candidates[0] if candidates[0]['confidence'] > 50 else None

# 创建矩形验证器
rect_validator = EnhancedRectangleValidator()

# 8. 部分矩形检测器
class PartialRectangleDetector:
    def __init__(self):
        self.edge_threshold_low = 30
        self.edge_threshold_high = 100
        
    def detect_partial_rectangles(self, img):
        """检测部分可见的矩形"""
        partial_candidates = []
        
        try:
            # 1. 边缘检测方法
            edge_candidates = self.detect_edge_based(img)
            partial_candidates.extend(edge_candidates)
            
            # 2. 轮廓检测方法
            contour_candidates = self.detect_contour_based(img)
            partial_candidates.extend(contour_candidates)
            
            return partial_candidates
            
        except Exception as e:
            print("部分矩形检测错误: " + str(e))
            return []
    
    def detect_edge_based(self, img):
        """基于边缘的部分矩形检测"""
        candidates = []
        
        try:
            # 检测接触边界的blob
            blobs = img.find_blobs(
                [(0, 80)],  # 检测暗色区域
                pixels_threshold=100,
                area_threshold=200,
                merge=True
            )
            
            for blob in blobs:
                x, y, w, h = blob.rect()
                
                # 检查是否接触边界
                touches_left = x <= BORDER_TOLERANCE
                touches_right = x + w >= img.width() - BORDER_TOLERANCE
                touches_top = y <= BORDER_TOLERANCE
                touches_bottom = y + h >= img.height() - BORDER_TOLERANCE
                
                if touches_left or touches_right or touches_top or touches_bottom:
                    # 检查形状特征
                    aspect_ratio = w / max(h, 1)
                    if 0.3 < aspect_ratio < 5.0 and blob.pixels() > 200:
                        
                        # 估算完整矩形中心
                        estimated_center = self.estimate_full_center(blob, img.width(), img.height())
                        
                        if estimated_center:
                            confidence = self.calculate_partial_confidence(blob, touches_left, touches_right, touches_top, touches_bottom)
                            
                            candidates.append({
                                'center': estimated_center,
                                'confidence': confidence,
                                'type': 'partial_edge',
                                'blob': blob
                            })
            
            return candidates
            
        except:
            return []
    
    def detect_contour_based(self, img):
        """基于轮廓的部分矩形检测"""
        candidates = []
        
        try:
            # 使用更宽松的参数检测轮廓
            gray_img = img.to_grayscale()
            
            # 寻找线段
            lines = img.find_lines(threshold=800, theta_margin=30, rho_margin=30)
            
            if len(lines) >= 2:
                # 分析线段，寻找可能的矩形边
                horizontal_lines = []
                vertical_lines = []
                
                for line in lines:
                    angle = line.theta()
                    length = math.sqrt((line.x2() - line.x1())**2 + (line.y2() - line.y1())**2)
                    
                    if length > 30:  # 只考虑足够长的线段
                        if abs(angle) < 20 or abs(angle - 180) < 20:
                            horizontal_lines.append(line)
                        elif abs(angle - 90) < 20:
                            vertical_lines.append(line)
                
                # 寻找线段组合
                for h_line in horizontal_lines:
                    for v_line in vertical_lines:
                        intersection = self.find_line_intersection(h_line, v_line)
                        if intersection and self.is_near_border(intersection, img.width(), img.height()):
                            estimated_center = self.estimate_center_from_lines(h_line, v_line, img.width(), img.height())
                            if estimated_center:
                                candidates.append({
                                    'center': estimated_center,
                                    'confidence': 65,
                                    'type': 'partial_line',
                                    'lines': [h_line, v_line]
                                })
            
            return candidates
            
        except:
            return []
    
    def estimate_full_center(self, blob, img_width, img_height):
        """从blob估算完整矩形中心"""
        x, y, w, h = blob.rect()
        blob_cx = x + w/2
        blob_cy = y + h/2
        
        # 根据blob位置和标准矩形尺寸估算
        est_cx = blob_cx
        est_cy = blob_cy
        
        # 根据接触的边界调整估算
        if x <= BORDER_TOLERANCE:  # 接触左边界
            est_cx = blob_cx + RECT_WIDTH * 0.25
        elif x + w >= img_width - BORDER_TOLERANCE:  # 接触右边界
            est_cx = blob_cx - RECT_WIDTH * 0.25
            
        if y <= BORDER_TOLERANCE:  # 接触上边界
            est_cy = blob_cy + RECT_HEIGHT * 0.25
        elif y + h >= img_height - BORDER_TOLERANCE:  # 接触下边界
            est_cy = blob_cy - RECT_HEIGHT * 0.25
        
        # 确保估算中心在合理范围内
        est_cx = max(50, min(img_width - 50, est_cx))
        est_cy = max(30, min(img_height - 30, est_cy))
        
        return (est_cx, est_cy)
    
    def calculate_partial_confidence(self, blob, touches_left, touches_right, touches_top, touches_bottom):
        """计算部分矩形的置信度"""
        confidence = 40  # 基础分数
        
        x, y, w, h = blob.rect()
        aspect_ratio = w / max(h, 1)
        area = blob.pixels()
        
        # 根据宽高比调整
        if 1.5 < aspect_ratio < 3.0:
            confidence += 20
        elif 1.0 < aspect_ratio < 4.0:
            confidence += 10
        
        # 根据面积调整
        if 500 < area < 5000:
            confidence += 15
        elif 200 < area < 10000:
            confidence += 10
        
        # 根据接触边界数量调整
        touch_count = sum([touches_left, touches_right, touches_top, touches_bottom])
        if touch_count == 1:
            confidence += 10
        elif touch_count == 2:
            confidence += 5
        
        return min(confidence, 85)  # 部分检测最高85分
    
    def find_line_intersection(self, line1, line2):
        """计算两条线的交点"""
        try:
            x1, y1, x2, y2 = line1.x1(), line1.y1(), line1.x2(), line1.y2()
            x3, y3, x4, y4 = line2.x1(), line2.y1(), line2.x2(), line2.y2()
            
            denom = (x1-x2)*(y3-y4) - (y1-y2)*(x3-x4)
            if abs(denom) < 1e-6:
                return None
                
            t = ((x1-x3)*(y3-y4) - (y1-y3)*(x3-x4)) / denom
            
            px = x1 + t*(x2-x1)
            py = y1 + t*(y2-y1)
            
            return (px, py)
        except:
            return None
    
    def is_near_border(self, point, width, height):
        """检查点是否靠近边界"""
        x, y = point
        return (x < BORDER_TOLERANCE or x > width - BORDER_TOLERANCE or 
                y < BORDER_TOLERANCE or y > height - BORDER_TOLERANCE)
    
    def estimate_center_from_lines(self, h_line, v_line, width, height):
        """从线段估算中心"""
        intersection = self.find_line_intersection(h_line, v_line)
        if not intersection:
            return None
            
        ix, iy = intersection
        
        # 简单的中心估算
        if ix < width/2:
            center_x = ix + RECT_WIDTH * 0.3
        else:
            center_x = ix - RECT_WIDTH * 0.3
            
        if iy < height/2:
            center_y = iy + RECT_HEIGHT * 0.3
        else:
            center_y = iy - RECT_HEIGHT * 0.3
        
        center_x = max(50, min(width - 50, center_x))
        center_y = max(30, min(height - 30, center_y))
        
        return (center_x, center_y)

# 创建部分矩形检测器
partial_detector = PartialRectangleDetector()

# 9. 目标稳定器
class TargetStabilizer:
    def __init__(self, max_history=5, stability_threshold=10):
        self.center_history = []
        self.max_history = max_history
        self.stability_threshold = stability_threshold
        self.stable_center = None
        self.lost_count = 0
        self.max_lost_frames = 10
        
    def update(self, center):
        if center is None:
            self.lost_count += 1
            if self.lost_count > self.max_lost_frames:
                self.stable_center = None
                self.center_history = []
            return self.stable_center
        
        self.lost_count = 0
        self.center_history.append(center)
        
        if len(self.center_history) > self.max_history:
            self.center_history.pop(0)
        
        if len(self.center_history) >= 3:
            x_coords = [c[0] for c in self.center_history]
            y_coords = [c[1] for c in self.center_history]
            
            x_mean = sum(x_coords) / len(x_coords)
            y_mean = sum(y_coords) / len(y_coords)
            
            x_var = sum((x - x_mean)**2 for x in x_coords) / len(x_coords)
            y_var = sum((y - y_mean)**2 for y in y_coords) / len(y_coords)
            
            if x_var < self.stability_threshold and y_var < self.stability_threshold:
                self.stable_center = (x_mean, y_mean)
            else:
                weights = [i+1 for i in range(len(self.center_history))]
                total_weight = sum(weights)
                
                weighted_x = sum(x_coords[i] * weights[i] for i in range(len(x_coords))) / total_weight
                weighted_y = sum(y_coords[i] * weights[i] for i in range(len(y_coords))) / total_weight
                
                self.stable_center = (weighted_x, weighted_y)
        else:
            self.stable_center = center
            
        return self.stable_center
    
    def is_stable(self):
        return len(self.center_history) >= 3 and self.stable_center is not None

# 创建目标稳定器
target_stabilizer = TargetStabilizer(max_history=TARGET_MAX_HISTORY, stability_threshold=TARGET_STABILITY_THRESHOLD)

# 10. 云台控制器
class GimbalController:
    def __init__(self, dead_zone=2.0, min_send_interval=50):
        self.dead_zone = dead_zone
        self.min_send_interval = min_send_interval
        self.last_send_time = 0
        self.last_pan_cmd = 0
        self.last_tilt_cmd = 0
        
    def send_command(self, pan_cmd, tilt_cmd, rect_x=0, rect_y=0):
        try:
            current_time = ticks_ms()
            
            pan_cmd = float(pan_cmd) if pan_cmd is not None else 0.0
            tilt_cmd = float(tilt_cmd) if tilt_cmd is not None else 0.0
            rect_x = int(rect_x) if rect_x is not None else 0
            rect_y = int(rect_y) if rect_y is not None else 0

            pan_diff = abs(pan_cmd - self.last_pan_cmd)
            tilt_diff = abs(tilt_cmd - self.last_tilt_cmd)
            
            if pan_diff < self.dead_zone and tilt_diff < self.dead_zone:
                return False
            
            if current_time - self.last_send_time < self.min_send_interval:
                return False
            
            pan_cmd = max(-50, min(50, pan_cmd))
            tilt_cmd = max(-50, min(50, tilt_cmd))

            pan_int = int(pan_cmd * 10) + 1000
            tilt_int = int(tilt_cmd * 10) + 1000

            rx_order = [0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFE]
            rx_order[1] = (pan_int >> 8) & 0xFF
            rx_order[2] = pan_int & 0xFF
            rx_order[3] = (tilt_int >> 8) & 0xFF
            rx_order[4] = tilt_int & 0xFF
            rx_order[5] = (rect_x >> 8) & 0xFF
            rx_order[6] = rect_x & 0xFF
            rx_order[7] = (rect_y >> 8) & 0xFF
            rx_order[8] = rect_y & 0xFF

            uart2.write(bytes(rx_order))
            
            self.last_send_time = current_time
            self.last_pan_cmd = pan_cmd
            self.last_tilt_cmd = tilt_cmd
            
            print("云台指令: Pan=" + str(round(pan_cmd, 2)) + ", Tilt=" + str(round(tilt_cmd, 2)))
            return True
            
        except:
            print("发送指令失败")
            return False

# 创建云台控制器
gimbal_controller = GimbalController(dead_zone=GIMBAL_DEAD_ZONE, min_send_interval=GIMBAL_MIN_SEND_INTERVAL)

# 11. 辅助函数
def calculate_center(corners):
    if len(corners) != 4:
        return None
    try:
        cx = sum(p[0] for p in corners) / 4
        cy = sum(p[1] for p in corners) / 4
        return (cx, cy)
    except:
        return None

def create_virtual_corners(center, width=60, height=40):
    """为部分检测创建虚拟角点"""
    cx, cy = center
    return [
        (cx - width/2, cy - height/2),
        (cx + width/2, cy - height/2),
        (cx + width/2, cy + height/2),
        (cx - width/2, cy + height/2)
    ]

# 12. 时钟初始化
clock = time.clock()

print("增强版矩形追踪系统启动")
print("特性: 高精度识别 + 部分矩形检测")
print("PID参数: X轴(Kp=" + str(PID_X_KP) + "), Y轴(Kp=" + str(PID_Y_KP) + ")")

# 13. 主循环
while True:
    clock.tick()
    img = sensor.snapshot()
    
    # 1. 完整矩形检测
    rects = cv_lite.grayscale_find_rectangles_with_corners(
        img.to_grayscale(),
        threshold=(0, 100),
        area_threshold=200,  # 降低面积阈值以检测更多候选
        pixels_threshold=150,
        approx_epsilon=0.03  # 提高拟合精度
    )
    
    # 2. 验证和筛选完整矩形
    complete_candidates = []
    for rect in rects:
        x, y, w, h = rect[0], rect[1], rect[2], rect[3]
        corners = [
            (rect[4], rect[5]), (rect[6], rect[7]),
            (rect[8], rect[9]), (rect[10], rect[11])
        ]

        is_valid, confidence = rect_validator.is_valid_rectangle(corners)
        if is_valid:
            center = calculate_center(corners)
            if center:
                complete_candidates.append({
                    'center': center,
                    'corners': corners,
                    'confidence': confidence,
                    'type': 'complete',
                    'area': w * h
                })

    # 3. 部分矩形检测
    partial_candidates = partial_detector.detect_partial_rectangles(img)
    
    # 4. 合并所有候选并选择最佳
    all_candidates = complete_candidates + partial_candidates
    best_detection = rect_validator.get_best_rectangle(all_candidates)
    
    # 5. 准备显示用的角点
    display_corners = None
    if best_detection:
        if best_detection['type'] == 'complete':
            display_corners = best_detection['corners']
        else:
            # 为部分检测创建虚拟角点用于显示
            display_corners = create_virtual_corners(best_detection['center'])
        
        # 添加到历史记录
        rect_validator.add_to_history(best_detection)

    # 6. 追踪控制
    if best_detection and display_corners:
        center = best_detection['center']
        cx, cy = center
        
        # 使用目标稳定器
        stable_center = target_stabilizer.update((cx, cy))
        
        if stable_center and target_stabilizer.is_stable():
            stable_cx, stable_cy = stable_center
            
            # 计算误差
            image_center_x = sensor.width() / 2
            image_center_y = sensor.height() / 2
            error_x = stable_cx - image_center_x
            error_y = stable_cy - image_center_y
            
            # PID控制
            pan_cmd = pid_x.compute(error_x)
            tilt_cmd = pid_y.compute(error_y)
            
            # 发送控制指令
            gimbal_controller.send_command(pan_cmd, tilt_cmd, int(stable_cx), int(stable_cy))
            
            # 绘制稳定中心点
            img.draw_circle(int(stable_cx), int(stable_cy), 6, color=(255, 255, 0), thickness=2)
        else:
            if not target_stabilizer.is_stable():
                pid_x.reset()
                pid_y.reset()

        # 绘制检测结果
        detection_type = best_detection['type']
        confidence = best_detection['confidence']
        
        if detection_type == 'complete':
            border_color = (0, 255, 0)  # 绿色 - 完整矩形
            corner_color = (0, 255, 0)
        elif 'partial' in detection_type:
            border_color = (255, 165, 0)  # 橙色 - 部分矩形
            corner_color = (255, 255, 0)
        else:
            border_color = (255, 0, 0)  # 红色 - 其他
            corner_color = (255, 0, 0)
            
        # 绘制矩形边框
        for i in range(4):
            x1, y1 = display_corners[i]
            x2, y2 = display_corners[(i+1) % 4]
            img.draw_line(int(x1), int(y1), int(x2), int(y2), color=border_color, thickness=2)
        
        # 绘制角点
        for p in display_corners:
            img.draw_circle(int(p[0]), int(p[1]), 4, color=corner_color, thickness=2)
            
        # 显示置信度
        img.draw_string_advanced(int(cx)+10, int(cy)-20, 14, 
                               str(int(confidence)) + "%", color=(255, 255, 255))
    else:
        target_stabilizer.update(None)

    # 7. 显示信息
    fps = clock.fps()
    img.draw_string_advanced(10, 10, 16, "FPS: " + str(round(fps, 1)), color=(255, 255, 255))
    
    # 显示检测统计
    complete_count = len(complete_candidates)
    partial_count = len(partial_candidates)
    img.draw_string_advanced(10, 30, 16, "Complete: " + str(complete_count), color=(0, 255, 0))
    img.draw_string_advanced(10, 50, 16, "Partial: " + str(partial_count), color=(255, 165, 0))
    
    # 显示目标状态
    if target_stabilizer.stable_center:
        status_text = "TRACKING" if target_stabilizer.is_stable() else "UNSTABLE"
        status_color = (0, 255, 0) if target_stabilizer.is_stable() else (255, 255, 0)
        img.draw_string_advanced(10, 70, 16, "Status: " + status_text, color=status_color)
        
        cx, cy = target_stabilizer.stable_center
        img.draw_string_advanced(10, 90, 16, "Target: (" + str(round(cx, 1)) + "," + str(round(cy, 1)) + ")", color=(255, 255, 255))
    else:
        img.draw_string_advanced(10, 70, 16, "Status: NO TARGET", color=(255, 0, 0))
    
    # 显示最佳检测信息
    if best_detection:
        detection_type = best_detection['type']
        confidence = best_detection['confidence']
        type_color = (0, 255, 0) if detection_type == 'complete' else (255, 165, 0)
        img.draw_string_advanced(10, 110, 16, "Best: " + detection_type + " (" + str(int(confidence)) + "%)", color=type_color)

    # 显示图像
    Display.show_image(img,
                      x=round((lcd_width-sensor.width())/2),
                      y=round((lcd_height-sensor.height())/2))

    # 控制台输出
    if int(fps * 10) % 20 == 0:
        target_info = "Target: " + str(target_stabilizer.stable_center) if target_stabilizer.stable_center else "No target"
        detection_info = ""
        if best_detection:
            detection_info = " [" + best_detection['type'] + ":" + str(int(best_detection['confidence'])) + "%]"
        print("FPS: " + str(round(fps, 1)) + ", " + target_info + detection_info + ", Complete:" + str(complete_count) + ", Partial:" + str(partial_count))
