import time, os, sys
import math
import cv_lite  # 导入cv_lite扩展模块
import ulab.numpy as np  # 导入numpy库
from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms
from machine import UART, FPIOA

# 导入配置文件
try:
    from config import *
    print("配置文件加载成功")
    # 验证配置参数
    config_errors = validate_config()
    if config_errors:
        print("配置参数警告:")
        for error in config_errors:
            print(f"  - {error}")
except ImportError:
    print("未找到config.py，使用默认参数")
    # 默认参数
    PID_X_KP, PID_X_KI, PID_X_KD = 70.0,15.0,30.0
    PID_Y_KP, PID_Y_KI, PID_Y_KD = 70.0,15.0,30.0
    TARGET_MAX_HISTORY, TARGET_STABILITY_THRESHOLD = 5, 5
    GIMBAL_DEAD_ZONE, GIMBAL_MIN_SEND_INTERVAL = 1.5, 80

# --------------------------- 硬件初始化 ---------------------------
# 串口初始化
fpioa = FPIOA()
fpioa.set_function(5, FPIOA.UART2_TXD)
fpioa.set_function(6, FPIOA.UART2_RXD)
uart2 = UART(UART.UART2, 115200)

# 屏幕分辨率设置
lcd_width = 800
lcd_height = 480

# 摄像头初始化（注意：保留RGB模式用于色块检测，后续转为灰度图用于矩形检测）
sensor = Sensor(width=1280, height=960)
sensor.reset()
sensor.set_framesize(width=320, height=240)  # 降低分辨率提高帧率
sensor.set_pixformat(Sensor.RGB565)  # 保留彩色用于紫色色块检测


# 显示初始化
Display.init(Display.ST7701, width=lcd_width, height=lcd_height, to_ide=True)
MediaManager.init()
sensor.run()

sensor.set_vflip(True)
sensor.set_hmirror(True)

# --------------------------- 配置参数 ---------------------------
# 矩形检测核心参数（基于cv_lite）
canny_thresh1      = 50        # Canny边缘检测低阈值
canny_thresh2      = 150       # Canny边缘检测高阈值
approx_epsilon     = 0.04      # 多边形拟合精度（越小越精确）
area_min_ratio     = 0.005     # 最小面积比例（相对于图像总面积）
max_angle_cos      = 0.3       # 角度余弦阈值（越小越接近矩形）
gaussian_blur_size = 3         # 高斯模糊核尺寸（奇数）

# 原有筛选参数
MIN_AREA = 100               # 最小面积阈值
MAX_AREA = 100000             # 最大面积阈值
MIN_ASPECT_RATIO = 0.3        # 最小宽高比
MAX_ASPECT_RATIO = 3.0        # 最大宽高比

# 虚拟坐标与圆形参数
BASE_RADIUS = 45              # 基础半径（虚拟坐标单位）
POINTS_PER_CIRCLE = 24        # 圆形采样点数量
PURPLE_THRESHOLD = (20, 60, 15, 70, -70, -20)  # 紫色色块阈值

# 基础矩形参数（固定方向，不再自动切换）
RECT_WIDTH = 210    # 固定矩形宽度
RECT_HEIGHT = 95    # 固定矩形高度
# 移除自动切换方向的逻辑，始终使用固定宽高的虚拟矩形

# --------------------------- 工具函数 ---------------------------
def calculate_distance(p1, p2):
    return math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])** 2)

def calculate_center(points):
    if not points:
        return (0, 0)
    sum_x = sum(p[0] for p in points)
    sum_y = sum(p[1] for p in points)
    return (sum_x / len(points), sum_y / len(points))

def is_valid_rect(corners):
    edges = [calculate_distance(corners[i], corners[(i+1)%4]) for i in range(4)]

    # 对边比例校验
    ratio1 = edges[0] / max(edges[2], 0.1)
    ratio2 = edges[1] / max(edges[3], 0.1)
    valid_ratio = 0.5 < ratio1 < 1.5 and 0.5 < ratio2 < 1.5

    # 面积校验
    area = 0
    for i in range(4):
        x1, y1 = corners[i]
        x2, y2 = corners[(i+1) % 4]
        area += (x1 * y2 - x2 * y1)
    area = abs(area) / 2
    valid_area = MIN_AREA < area < MAX_AREA

    # 宽高比校验
    width = max(p[0] for p in corners) - min(p[0] for p in corners)
    height = max(p[1] for p in corners) - min(p[1] for p in corners)
    aspect_ratio = width / max(height, 0.1)
    valid_aspect = MIN_ASPECT_RATIO < aspect_ratio < MAX_ASPECT_RATIO

    return valid_ratio and valid_area and valid_aspect

def detect_purple_blobs(img):
    return img.find_blobs(
        [PURPLE_THRESHOLD],
        pixels_threshold=100,
        area_threshold=100,
        merge=True
    )

#def send_circle_points(points):
#    if not points:
#        return
#    count = len(points)
#    msg = f"$$C,{count},"
#    for x, y in points:
#        msg += f"{x},{y},"
#    msg = msg.rstrip(',') + "##"
#    uart.write(msg)
    # print(f"发送圆形点: {msg}")

def get_perspective_matrix(src_pts, dst_pts):
    """计算透视变换矩阵"""
    A = []
    B = []
    for i in range(4):
        x, y = src_pts[i]
        u, v = dst_pts[i]
        A.append([x, y, 1, 0, 0, 0, -u*x, -u*y])
        A.append([0, 0, 0, x, y, 1, -v*x, -v*y])
        B.append(u)
        B.append(v)

    # 高斯消元求解矩阵
    n = 8
    for i in range(n):
        max_row = i
        for j in range(i, len(A)):
            if abs(A[j][i]) > abs(A[max_row][i]):
                max_row = j
        A[i], A[max_row] = A[max_row], A[i]
        B[i], B[max_row] = B[max_row], B[i]

        pivot = A[i][i]
        if abs(pivot) < 1e-8:
            return None
        for j in range(i, n):
            A[i][j] /= pivot
        B[i] /= pivot

        for j in range(len(A)):
            if j != i and A[j][i] != 0:
                factor = A[j][i]
                for k in range(i, n):
                    A[j][k] -= factor * A[i][k]
                B[j] -= factor * B[i]

    return [
        [B[0], B[1], B[2]],
        [B[3], B[4], B[5]],
        [B[6], B[7], 1.0]
    ]

def transform_points(points, matrix):
    """应用透视变换将虚拟坐标映射到原始图像坐标"""
    transformed = []
    for (x, y) in points:
        x_hom = x * matrix[0][0] + y * matrix[0][1] + matrix[0][2]
        y_hom = x * matrix[1][0] + y * matrix[1][1] + matrix[1][2]
        w_hom = x * matrix[2][0] + y * matrix[2][1] + matrix[2][2]
        if abs(w_hom) > 1e-8:
            transformed.append((x_hom / w_hom, y_hom / w_hom))
    return transformed

def sort_corners(corners):
    """将矩形角点按左上、右上、右下、左下顺序排序"""
    center = calculate_center(corners)
    sorted_corners = sorted(corners, key=lambda p: math.atan2(p[1]-center[1], p[0]-center[0]))

    # 调整顺序为左上、右上、右下、左下
    if len(sorted_corners) == 4:
        left_top = min(sorted_corners, key=lambda p: p[0]+p[1])
        index = sorted_corners.index(left_top)
        sorted_corners = sorted_corners[index:] + sorted_corners[:index]
    return sorted_corners

def get_rectangle_orientation(corners):
    """计算矩形的主方向角（水平边与x轴的夹角）"""
    if len(corners) != 4:
        return 0

    # 计算上边和右边的向量
    top_edge = (corners[1][0] - corners[0][0], corners[1][1] - corners[0][1])
    right_edge = (corners[2][0] - corners[1][0], corners[2][1] - corners[1][1])

    # 选择较长的边作为主方向
    if calculate_distance(corners[0], corners[1]) > calculate_distance(corners[1], corners[2]):
        main_edge = top_edge
    else:
        main_edge = right_edge

    # 计算主方向角（弧度）
    angle = math.atan2(main_edge[1], main_edge[0])
    return angle

# 6. 改进的PID控制器
class ImprovedPID:
    def __init__(self, kp=0.8, ki=0.02, kd=0.15, max_output=30, integral_limit=20):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.max_output = max_output
        self.integral_limit = integral_limit
        self.prev_error = 0
        self.integral = 0
        self.prev_time = ticks_ms()
        self.error_history = []  # 用于滤波
        self.max_history = 3

    def compute(self, error):
        current_time = ticks_ms()
        dt = (current_time - self.prev_time) / 1000.0
        if dt <= 0:
            dt = 0.01

        # 误差滤波 - 使用移动平均
        self.error_history.append(error)
        if len(self.error_history) > self.max_history:
            self.error_history.pop(0)
        filtered_error = sum(self.error_history) / len(self.error_history)

        # 积分项计算（带限幅）
        self.integral += filtered_error * dt
        if self.integral > self.integral_limit:
            self.integral = self.integral_limit
        elif self.integral < -self.integral_limit:
            self.integral = -self.integral_limit

        # 微分项计算
        derivative = (filtered_error - self.prev_error) / dt

        # PID输出计算
        output = self.kp * filtered_error + self.ki * self.integral + self.kd * derivative

        # 输出限幅
        if output > self.max_output:
            output = self.max_output
        elif output < -self.max_output:
            output = -self.max_output

        self.prev_error = filtered_error
        self.prev_time = current_time
        return output

    def reset(self):
        """重置PID状态"""
        self.prev_error = 0
        self.integral = 0
        self.error_history = []

# 创建PID控制器实例（使用配置文件参数）
try:
    pid_x_config = get_pid_x_config()
    pid_y_config = get_pid_y_config()
    pid_x = ImprovedPID(**pid_x_config)
    pid_y = ImprovedPID(**pid_y_config)
    print(f"PID参数: X轴({pid_x_config}), Y轴({pid_y_config})")
except:
    # 使用默认参数
    pid_x = ImprovedPID(kp=PID_X_KP, ki=PID_X_KI, kd=PID_X_KD, max_output=25, integral_limit=15)
    pid_y = ImprovedPID(kp=PID_Y_KP, ki=PID_Y_KI, kd=PID_Y_KD, max_output=20, integral_limit=12)
    print("使用默认PID参数")

# 7. 目标稳定性检测和滤波
class TargetStabilizer:
    def __init__(self, max_history=5, stability_threshold=10):
        self.center_history = []
        self.max_history = max_history
        self.stability_threshold = stability_threshold
        self.stable_center = None
        self.lost_count = 0
        self.max_lost_frames = 10  # 最大丢失帧数

    def update(self, center):
        """更新目标中心，返回稳定的中心坐标"""
        if center is None:
            self.lost_count += 1
            if self.lost_count > self.max_lost_frames:
                self.stable_center = None
                self.center_history = []
            return self.stable_center

        self.lost_count = 0
        self.center_history.append(center)

        if len(self.center_history) > self.max_history:
            self.center_history.pop(0)

        # 计算中心点的稳定性
        if len(self.center_history) >= 3:
            # 计算最近几帧的方差
            x_coords = [c[0] for c in self.center_history]
            y_coords = [c[1] for c in self.center_history]

            x_mean = sum(x_coords) / len(x_coords)
            y_mean = sum(y_coords) / len(y_coords)

            x_var = sum((x - x_mean)**2 for x in x_coords) / len(x_coords)
            y_var = sum((y - y_mean)**2 for y in y_coords) / len(y_coords)

            # 如果方差小于阈值，认为目标稳定
            if x_var < self.stability_threshold and y_var < self.stability_threshold:
                self.stable_center = (x_mean, y_mean)
            else:
                # 使用加权平均，最新的帧权重更大
                weights = [i+1 for i in range(len(self.center_history))]
                total_weight = sum(weights)

                weighted_x = sum(x_coords[i] * weights[i] for i in range(len(x_coords))) / total_weight
                weighted_y = sum(y_coords[i] * weights[i] for i in range(len(y_coords))) / total_weight

                self.stable_center = (weighted_x, weighted_y)
        else:
            self.stable_center = center

        return self.stable_center

    def is_stable(self):
        """检查目标是否稳定"""
        return len(self.center_history) >= 3 and self.stable_center is not None

# 创建目标稳定器（使用配置文件参数）
try:
    stabilizer_config = get_target_stabilizer_config()
    target_stabilizer = TargetStabilizer(**stabilizer_config)
    print(f"目标稳定器参数: {stabilizer_config}")
except:
    target_stabilizer = TargetStabilizer(max_history=TARGET_MAX_HISTORY, stability_threshold=TARGET_STABILITY_THRESHOLD)
    print("使用默认稳定器参数")

# 8. 改进的发送指令函数
class GimbalController:
    def __init__(self, dead_zone=2.0, min_send_interval=50):
        self.dead_zone = dead_zone  # 死区阈值
        self.min_send_interval = min_send_interval  # 最小发送间隔(ms)
        self.last_send_time = 0
        self.last_pan_cmd = 0
        self.last_tilt_cmd = 0

    def send_command(self, pan_cmd, tilt_cmd, rect_x=0, rect_y=0):
        """发送云台控制指令（带死区和频率控制）"""
        try:
            current_time = ticks_ms()

            # 参数处理
            pan_cmd = float(pan_cmd) if pan_cmd is not None else 0.0
            tilt_cmd = float(tilt_cmd) if tilt_cmd is not None else 0.0
            rect_x = int(rect_x) if rect_x is not None else 0
            rect_y = int(rect_y) if rect_y is not None else 0

            # 死区处理 - 如果指令变化很小，则不发送
            pan_diff = abs(pan_cmd - self.last_pan_cmd)
            tilt_diff = abs(tilt_cmd - self.last_tilt_cmd)

            if pan_diff < self.dead_zone and tilt_diff < self.dead_zone:
                return False  # 在死区内，不发送指令

            # 频率控制 - 限制发送频率
            if current_time - self.last_send_time < self.min_send_interval:
                return False  # 发送太频繁，跳过

            # 指令限幅
            pan_cmd = max(-50, min(50, pan_cmd))
            tilt_cmd = max(-50, min(50, tilt_cmd))

            # 编码指令
            pan_int = int(pan_cmd * 10) + 1000
            tilt_int = int(tilt_cmd * 10) + 1000

            rx_order = [0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFE]
            rx_order[1] = (pan_int >> 8) & 0xFF
            rx_order[2] = pan_int & 0xFF
            rx_order[3] = (tilt_int >> 8) & 0xFF
            rx_order[4] = tilt_int & 0xFF
            rx_order[5] = (rect_x >> 8) & 0xFF
            rx_order[6] = rect_x & 0xFF
            rx_order[7] = (rect_y >> 8) & 0xFF
            rx_order[8] = rect_y & 0xFF

            uart2.write(bytes(rx_order))

            # 更新状态
            self.last_send_time = current_time
            self.last_pan_cmd = pan_cmd
            self.last_tilt_cmd = tilt_cmd

            print(f"云台指令: Pan={pan_cmd:.2f}, Tilt={tilt_cmd:.2f}, X={rect_x}, Y={rect_y}")
            return True

        except Exception as e:
            print(f"发送指令失败: {e}")
            return False

# 创建云台控制器（使用配置文件参数）
try:
    gimbal_config = get_gimbal_controller_config()
    gimbal_controller = GimbalController(**gimbal_config)
    print(f"云台控制器参数: {gimbal_config}")
except:
    gimbal_controller = GimbalController(dead_zone=GIMBAL_DEAD_ZONE, min_send_interval=GIMBAL_MIN_SEND_INTERVAL)
    print("使用默认云台控制器参数")

# --------------------------- 主循环 ---------------------------
clock = time.clock()
image_shape = [sensor.height(), sensor.width()]  # [高, 宽] 用于cv_lite
while True:
    clock.tick()
    img = sensor.snapshot()

    # 1. 检测紫色色块（保留原有功能）
    purple_blobs = detect_purple_blobs(img)
    for blob in purple_blobs:
        img.draw_rectangle(blob[0:4], color=(255, 0, 255), thickness=1)
        img.draw_cross(blob.cx(), blob.cy(), color=(255, 0, 255), thickness=1)

    # 2. 矩形检测（使用cv_lite替换原有实现）
    # 2.1 将RGB图像转为灰度图（用于矩形检测）
    gray_img = img.to_grayscale()
    img_np = gray_img.to_numpy_ref()  # 转为numpy数组供cv_lite使用

    # 2.2 调用cv_lite矩形检测函数（带角点）
    rects = cv_lite.grayscale_find_rectangles_with_corners(
        image_shape,       # 图像尺寸 [高, 宽]
        img_np,            # 灰度图数据
        canny_thresh1,     # Canny低阈值
        canny_thresh2,     # Canny高阈值
        approx_epsilon,    # 多边形拟合精度
        area_min_ratio,    # 最小面积比例
        max_angle_cos,     # 角度余弦阈值
        gaussian_blur_size # 高斯模糊尺寸
    )

    # 3. 筛选最小矩形（保留原有逻辑）
    min_area = float('inf')
    smallest_rect = None
    smallest_rect_corners = None  # 存储最小矩形的角点

    for rect in rects:
        # rect格式: [x, y, w, h, c1.x, c1.y, c2.x, c2.y, c3.x, c3.y, c4.x, c4.y]
        x, y, w, h = rect[0], rect[1], rect[2], rect[3]
        # 提取四个角点
        corners = [
            (rect[4], rect[5]),   # 角点1
            (rect[6], rect[7]),   # 角点2
            (rect[8], rect[9]),   # 角点3
            (rect[10], rect[11])  # 角点4
        ]

        # 验证矩形有效性
        if is_valid_rect(corners):
            # 计算面积
            area = w * h  # 直接使用矩形宽高计算面积（更高效）
            # 更新最小矩形
            if area < min_area:
                min_area = area
                smallest_rect = (x, y, w, h)
                smallest_rect_corners = corners

    # 4. 处理最小矩形（修改后：固定虚拟矩形方向）
    if smallest_rect and smallest_rect_corners:
        x, y, w, h = smallest_rect
        corners = smallest_rect_corners

        # 对矩形角点进行排序
        sorted_corners = sort_corners(corners)

        # 绘制矩形边框和角点
        for i in range(4):
            x1, y1 = sorted_corners[i]
            x2, y2 = sorted_corners[(i+1) % 4]
            img.draw_line(x1, y1, x2, y2, color=(255, 0, 0), thickness=2)
        for p in sorted_corners:
            img.draw_circle(p[0], p[1], 5, color=(0, 255, 0), thickness=2)

        # 计算并绘制矩形中心点
        rect_center = calculate_center(sorted_corners)
        rect_center_int = (int(round(rect_center[0])), int(round(rect_center[1])))
        img.draw_circle(rect_center_int[0], rect_center_int[1], 4, color=(0, 255, 255), thickness=2)

        # 计算矩形主方向角（仅用于参考，不再影响虚拟矩形方向）
        angle = get_rectangle_orientation(sorted_corners)

        # 【核心修改】移除自动切换方向逻辑，固定使用预设的虚拟矩形尺寸和方向
        # 固定虚拟矩形（不再根据实际宽高比切换）
        virtual_rect = [
            (0, 0),
            (RECT_WIDTH, 0),
            (RECT_WIDTH, RECT_HEIGHT),
            (0, RECT_HEIGHT)
        ]

        # 【核心修改】固定圆形半径参数（不再根据实际宽高比调整）
        radius_x = BASE_RADIUS
        radius_y = BASE_RADIUS

        # 【核心修改】固定虚拟中心（基于固定的宽高）
        virtual_center = (RECT_WIDTH / 2, RECT_HEIGHT / 2)

        # 在虚拟矩形中生成椭圆点集（映射后为正圆）
        virtual_circle_points = []
        for i in range(POINTS_PER_CIRCLE):
            angle_rad = 2 * math.pi * i / POINTS_PER_CIRCLE
            x_virt = virtual_center[0] + radius_x * math.cos(angle_rad)
            y_virt = virtual_center[1] + radius_y * math.sin(angle_rad)
            virtual_circle_points.append((x_virt, y_virt))

        # 计算透视变换矩阵并映射坐标
        matrix = get_perspective_matrix(virtual_rect, sorted_corners)
        if matrix:
            mapped_points = transform_points(virtual_circle_points, matrix)
            int_points = [(int(round(x)), int(round(y))) for x, y in mapped_points]

            # 绘制圆形
            for (px, py) in int_points:
                img.draw_circle(px, py, 2, color=(255, 0, 255), thickness=2)

            # 绘制圆心
            mapped_center = transform_points([virtual_center], matrix)
            if mapped_center:
                cx, cy = map(int, map(round, mapped_center[0]))
                img.draw_circle(cx, cy, 3, color=(0, 0, 255), thickness=1)

            # 使用目标稳定器处理中心点
            stable_center = target_stabilizer.update((cx, cy))

            if stable_center and target_stabilizer.is_stable():
                stable_cx, stable_cy = stable_center

                # 计算视野中心（320x240分辨率）
                image_center_x = sensor.width() / 2   # 160
                image_center_y = sensor.height() / 2  # 120

                # 计算偏差（目标中心 - 视野中心）
                error_x = stable_cx - image_center_x
                error_y = stable_cy - image_center_y

                # PID控制计算
                pan_cmd = pid_x.compute(error_x)
                tilt_cmd = pid_y.compute(error_y)

                # 发送控制指令（使用改进的控制器）
                gimbal_controller.send_command(pan_cmd, tilt_cmd, int(stable_cx), int(stable_cy))

                # 绘制稳定中心点
                img.draw_circle(int(stable_cx), int(stable_cy), 6, color=(255, 255, 0), thickness=2)
                img.draw_string_advanced(int(stable_cx)+10, int(stable_cy), 16,
                                       f"Err:({error_x:.1f},{error_y:.1f})", color=(255, 255, 0))
            else:
                # 目标不稳定，重置PID
                if not target_stabilizer.is_stable():
                    pid_x.reset()
                    pid_y.reset()

            # 发送坐标
            #send_circle_points(int_points)

    # 5. 显示与性能统计
    fps = clock.fps()

    # 显示调试信息
    img.draw_string_advanced(10, 10, 16, f"FPS: {fps:.1f}", color=(255, 255, 255))

    # 显示目标状态
    if target_stabilizer.stable_center:
        status_text = "TRACKING" if target_stabilizer.is_stable() else "UNSTABLE"
        status_color = (0, 255, 0) if target_stabilizer.is_stable() else (255, 255, 0)
        img.draw_string_advanced(10, 30, 16, f"Status: {status_text}", color=status_color)

        # 显示目标中心坐标
        cx, cy = target_stabilizer.stable_center
        img.draw_string_advanced(10, 50, 16, f"Target: ({cx:.1f},{cy:.1f})", color=(255, 255, 255))
    else:
        img.draw_string_advanced(10, 30, 16, "Status: NO TARGET", color=(255, 0, 0))

    # 显示矩形检测数量
    img.draw_string_advanced(10, 70, 16, f"Rects: {len(rects)}", color=(255, 255, 255))

    # 显示图像
    Display.show_image(img,
                      x=round((lcd_width-sensor.width())/2),
                      y=round((lcd_height-sensor.height())/2))

    # 控制台输出（降低频率）
    if int(fps * 10) % 10 == 0:  # 每秒输出一次
        target_info = f"Target: {target_stabilizer.stable_center}" if target_stabilizer.stable_center else "No target"
        print(f"FPS: {fps:.1f}, {target_info}, Rects: {len(rects)}")
