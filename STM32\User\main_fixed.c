#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "nvic.h"
#include "usart.h"

int main(void)
{
    USART_INIT(); // 初始化串口
    NVIC_INIT();  // 配置NVIC优先级分组及使能串口接收中断
    
    float pan_raw = 0;     // 云台水平角度（来自上位机）
    float tilt_raw = 0;    // 云台垂直角度（来自上位机）
    
    int pan_command = 0;   // 云台水平角度控制值
    int tilt_command = 0;  // 云台垂直角度控制值
    
    // 调试变量
    uint32_t loop_count = 0;
    uint32_t x_exec_count = 0;
    uint32_t y_exec_count = 0;
    
    while (1)
    {    
        loop_count++;
        
        // 修复1: 统一数据转换比例
        // 原来X轴除以150，Y轴除以10，差异太大导致X轴响应不灵敏
        pan_raw = (float)(pan - 1000) / 10.0f;   // 改为与Y轴相同的比例
        tilt_raw = (float)(tilt - 1000) / 10.0f; // 保持原有比例
        
        pan_command = (int)(pan_raw);
        tilt_command = (int)(tilt_raw);
        
        // X轴控制（修复版）
        if(stepmotorx_Flag)
        {
            x_exec_count++;
            
            // 修复2: 确保即使是小的命令也能执行
            if(pan_command != 0) // 只要不为0就执行
            {
                if(pan_command > 0) // 需要右移动
                {
                    // 修复3: 统一步进电机速度，确保X轴和Y轴响应一致
                    StepperPacket_Fill(&stepper_packet_2, 0x01, 0x02, 0x01, 0x20, 
                                     abs(pan_command), 10); // 速度改为10，与Y轴一致
                }
                else // 需要左移动
                {
                    StepperPacket_Fill(&stepper_packet_2, 0x01, 0x02, 0x00, 0x20, 
                                     abs(pan_command), 10); // 速度改为10，与Y轴一致
                }
                
                // 确保数据包发送
                USART2_SendStepperPacket(&stepper_packet_2);
            }
            
            // 清除标志
            stepmotorx_Flag = 0;
        }
        
        // Y轴控制（保持原有逻辑）
        if(stepmotory_Flag)
        {
            y_exec_count++;
            
            if(tilt_command != 0)
            {
                if(tilt_command > 0) // 需要上移动
                {
                    StepperPacket_Fill(&stepper_packet_3, 0x02, 0x02, 0x01, 0x20, 
                                     abs(tilt_command), 10);
                }
                else // 需要下移动
                {
                    StepperPacket_Fill(&stepper_packet_3, 0x02, 0x02, 0x00, 0x20, 
                                     abs(tilt_command), 10);
                }    
                
                USART3_SendStepperPacket(&stepper_packet_3);
            }
            
            stepmotory_Flag = 0;
        }
        
        // 修复4: 添加定期状态输出用于调试
        if(loop_count % 10000 == 0) // 每10000次循环输出一次状态
        {
            // 通过串口1发送调试信息（如果连接了调试串口）
            char debug_info[100];
            sprintf(debug_info, "Loop:%lu, X_exec:%lu, Y_exec:%lu, pan:%d, tilt:%d\r\n", 
                   loop_count, x_exec_count, y_exec_count, (int)pan, (int)tilt);
            
            // 如果有调试串口，可以发送调试信息
            // USART_SendString(USART1, debug_info);
        }
        
        // 修复5: 减少延时，提高响应速度
        Delay_ms(1); // 保持1ms延时
    }
}
