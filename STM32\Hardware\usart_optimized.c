#include "stm32f10x.h"
#include "usart.h"

// 串口相关全局变量声明
volatile uint8_t USART1_RxData = 0;    // 串口1接收的当前字节数据
volatile uint8_t USART1_RxFlag = 0;    // 串口1接收完成标志
volatile uint8_t USART2_RxData = 0;    // 串口2接收的当前字节数据
volatile uint8_t USART2_RxFlag = 0;    // 串口2接收完成标志
volatile uint8_t USART3_RxData = 0;    // 串口3接收的当前字节数据
volatile uint8_t USART3_RxFlag = 0;    // 串口3接收完成标志
float pan = 1000;                      // 云台水平角度（来自上位机的角度）
float tilt = 1000;                     // 云台垂直角度（来自上位机的角度）
uint16_t rect_x = 0;                   // 目标矩形X坐标（像素）
uint16_t rect_y = 0;                   // 目标矩形Y坐标（像素）
volatile uint16_t uart1_rx_buf[RX_BUF1_SIZE] = {0}; // 串口1接收缓冲区
volatile uint16_t uart2_rx_buf[RX_BUF2_SIZE] = {0}; // 串口2接收缓冲区
volatile uint16_t uart3_rx_buf[RX_BUF3_SIZE] = {0}; // 串口3接收缓冲区
volatile uint8_t uart1_rx_pt = 0;      // 串口1接收缓冲区写入指针
volatile uint8_t uart2_rx_pt = 0;      // 串口2接收缓冲区写入指针
volatile uint8_t uart3_rx_pt = 0;      // 串口3接收缓冲区写入指针
StepperPacket_t stepper_packet_2; // 串口2步进电机数据包
StepperPacket_t stepper_packet_3; // 串口3步进电机数据包
volatile uint8_t stepmotorx_Flag = 0; // 步进电机X轴控制标志
volatile uint8_t stepmotory_Flag = 0; // 步进电机Y轴控制标志

// 数据滤波相关变量
static uint16_t last_pan = 1000;
static uint16_t last_tilt = 1000;
static uint8_t data_valid_count = 0;
static uint32_t last_valid_time = 0;

void USART_INIT(void)
{
    //开启USART1时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1, ENABLE);
    //开启usart2时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);
    //开启usart3时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART3, ENABLE);
    //开启GPIOA时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
    //开启GPIOB时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);  

    //配置GPIOA引脚
    GPIO_InitTypeDef GPIO_InitStructure;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;//复用推挽输出
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9; //USART1_TX
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOA, &GPIO_InitStructure); //对PA9引脚初始化
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU; //上拉输入
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10; //USART1_RX
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOA, &GPIO_InitStructure); //对PA10引脚初始化
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP; //复用推挽输出
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2; //USART2_TX
    GPIO_Init(GPIOA, &GPIO_InitStructure); //对PA2引脚初始化
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING; //浮空输入
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_3; //USART2_RX
    GPIO_Init(GPIOA, &GPIO_InitStructure); //对PA3引脚初始化
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP; //复用推挽输出
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10; //USART3_TX
    GPIO_Init(GPIOB, &GPIO_InitStructure); //对PB10引脚初始化
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING; //浮空输入
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_11; //USART3_RX
    GPIO_Init(GPIOB, &GPIO_InitStructure); //对PB11引脚初始化

    //配置USART1
    USART_InitTypeDef USART_InitStructure;
    USART_InitStructure.USART_BaudRate = 115200; //波特率
    USART_InitStructure.USART_WordLength = USART_WordLength_8b; //字长
    USART_InitStructure.USART_StopBits = USART_StopBits_1; //停止位
    USART_InitStructure.USART_Parity = USART_Parity_No; //无奇偶校验
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None; //无硬件流控制
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx; //接收和发送模式
    USART_Init(USART1, &USART_InitStructure);
    //配置USART2
    USART_InitStructure.USART_BaudRate = 115200; //波特率
    USART_InitStructure.USART_WordLength = USART_WordLength_8b; //字长
    USART_InitStructure.USART_StopBits = USART_StopBits_1; //停止位
    USART_InitStructure.USART_Parity = USART_Parity_No; //无奇偶校验
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None; //无硬件流控制
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx; //接收和发送模式
    USART_Init(USART2, &USART_InitStructure);
    //配置USART3
    USART_InitStructure.USART_BaudRate = 115200; //波特率
    USART_InitStructure.USART_WordLength = USART_WordLength_8b; //字长
    USART_InitStructure.USART_StopBits = USART_StopBits_1; //停止位
    USART_InitStructure.USART_Parity = USART_Parity_No; //无奇偶校验
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None; //无硬件流控制
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx; //接收和发送模式
    USART_Init(USART3, &USART_InitStructure);
    
    //使能USART1
    USART_Cmd(USART1, ENABLE);
    //使能USART2
    USART_Cmd(USART2, ENABLE); 
    //使能USART3
    USART_Cmd(USART3, ENABLE);
}

/**
  * 函  数：串口发送一个字节
  * 参  数：Byte 要发送的一个字节
  * 返 回 值：无
  */
 void USART_SendByte(USART_TypeDef* USARTx, uint8_t Byte)
{
    while (USART_GetFlagStatus(USARTx, USART_FLAG_TXE) == RESET); //等待发送完成
    USART_SendData(USARTx, Byte); //将字节数据写入数据寄存器，写入后USART自动生成时序波形
    while (USART_GetFlagStatus(USARTx, USART_FLAG_TC) == RESET); //等待发送完成
}

/**
  * 函  数：串口发送字符串
  * 参  数：String 要发送的字符串
  * 返 回 值：无
  */
void USART_SendString(USART_TypeDef* USARTx, char *String)
{
    while (*String) //遍历字符数组，直到遇到字符串结束标志'\0'
    {
        USART_SendByte(USARTx, *String++); //发送每个字符
    }
}

/**
  * 函  数：处理串口1接收数据帧解析 - 优化版本
  * 参  数：无
  * 返 回 值：无
  * 功  能：自动检测帧头和帧尾解析一帧数据，添加数据验证和滤波
  */
void UART1_rx_dataframe()
{
    USART1_RxData = USART_ReceiveData(USART1); //接收一个字节数据
    
    if (USART1_RxData == MY_UART1_HEADER) //判断是否为帧头
    {
        uart1_rx_pt = 0; //指针清零
        uart1_rx_buf[uart1_rx_pt++] = USART1_RxData; //存储帧头
    } 
    else if (USART1_RxData == MY_UART1_TAIL && uart1_rx_pt > 0) //判断是否为帧尾且已开始接收
    {
        uart1_rx_buf[uart1_rx_pt++] = USART1_RxData; //存储帧尾
        
        // 检查数据帧长度
        if (uart1_rx_pt == 10) // 完整帧长度
        {
            // 解析数据
            uint16_t new_pan = (uart1_rx_buf[1] << 8) | uart1_rx_buf[2];
            uint16_t new_tilt = (uart1_rx_buf[3] << 8) | uart1_rx_buf[4];
            uint16_t new_rect_x = (uart1_rx_buf[5] << 8) | uart1_rx_buf[6];
            uint16_t new_rect_y = (uart1_rx_buf[7] << 8) | uart1_rx_buf[8];
            
            // 数据有效性检查
            if (new_pan >= 500 && new_pan <= 1500 && 
                new_tilt >= 500 && new_tilt <= 1500)
            {
                // 数据变化检测 - 避免处理相同数据
                uint16_t pan_diff = (new_pan > last_pan) ? (new_pan - last_pan) : (last_pan - new_pan);
                uint16_t tilt_diff = (new_tilt > last_tilt) ? (new_tilt - last_tilt) : (last_tilt - new_tilt);
                
                // 只有当数据变化足够大时才更新
                if (pan_diff > 2 || tilt_diff > 2 || data_valid_count < 3)
                {
                    pan = new_pan;
                    tilt = new_tilt;
                    rect_x = new_rect_x;
                    rect_y = new_rect_y;
                    
                    last_pan = new_pan;
                    last_tilt = new_tilt;
                    
                    // 设置控制标志
                    if (pan_diff > 2) stepmotorx_Flag = 1;
                    if (tilt_diff > 2) stepmotory_Flag = 1;
                    
                    data_valid_count++;
                    if (data_valid_count > 10) data_valid_count = 10;
                }
            }
        }
        uart1_rx_pt = 0; //指针清零，准备下一帧
    } 
    else if (uart1_rx_pt < RX_BUF1_SIZE - 1) //中间数据，防止溢出
    {
        uart1_rx_buf[uart1_rx_pt++] = USART1_RxData; //存储数据
    }
    else
    {
        uart1_rx_pt = 0; // 溢出保护，重新开始
    }
}

// 步进电机数据包填充函数
// StepperPacket_t 数据包结构说明：
// header    : 帧头，固定为 0x7b 即 MY_UART2_HEADER
// id        : 电机号，01 表示电机2，02 表示电机3
// ctrl_mode : 控制模式，步进电机一般为 2
// dir       : 方向，0 逆转，1 正转
// microstep : 细分数，步进细分，常用 32
// angle_h   : 目标角度高8位（int16_t高字节）
// angle_l   : 目标角度低8位（int16_t低字节）
// speed_h   : 速度高8位（uint16_t高字节）
// speed_l   : 速度低8位（uint16_t低字节）
// checksum  : 校验位，前9字节异或
// tail      : 帧尾，固定为 0x7d 即 MY_UART2_TAIL
void StepperPacket_Fill(StepperPacket_t* pkt, uint8_t id, uint8_t ctrl_mode, uint8_t dir, uint8_t microstep, int16_t angle, uint16_t speed)
{
    pkt->header    = MY_UART2_HEADER ; // 帧头
    pkt->id        = id;               // 电机号
    pkt->ctrl_mode = ctrl_mode;        // 控制模式
    pkt->dir       = dir;              // 方向
    pkt->microstep = microstep;        // 细分数
    pkt->angle_h   = (angle >> 8) & 0xFF; // 角度高8位
    pkt->angle_l   = angle & 0xFF;        // 角度低8位
    pkt->speed_h   = (speed >> 8) & 0xFF; // 速度高8位
    pkt->speed_l   = speed & 0xFF;        // 速度低8位
    // 校验位计算前9字节
    uint8_t sum = pkt->header ^ pkt->id ^ pkt->ctrl_mode ^ pkt->dir ^ pkt->microstep ^ pkt->angle_h ^ pkt->angle_l ^ pkt->speed_h ^ pkt->speed_l;
    pkt->checksum = sum;                // 校验位
    pkt->tail     = MY_UART2_TAIL;      // 帧尾
}

// 串口2发送编号01电机数据包
void USART2_SendStepperPacket(const StepperPacket_t* pkt)
 {
    const uint8_t* p = (const uint8_t*)pkt;
    for(int i=0; i<sizeof(StepperPacket_t); i++) {
        while (USART_GetFlagStatus(USART2, USART_FLAG_TXE) == RESET);
        USART_SendData(USART2, p[i]);
        while (USART_GetFlagStatus(USART2, USART_FLAG_TC) == RESET);
    }
}

// 串口3发送编号02电机数据包
void USART3_SendStepperPacket(const StepperPacket_t* pkt)
 {
    const uint8_t* p = (const uint8_t*)pkt;
    for(int i=0; i<sizeof(StepperPacket_t); i++) {
        while (USART_GetFlagStatus(USART3, USART_FLAG_TXE) == RESET);
        USART_SendData(USART3, p[i]);
        while (USART_GetFlagStatus(USART3, USART_FLAG_TC) == RESET);
    }
}

// 中断处理函数
void USART1_IRQHandler(void)
{
    if (USART_GetITStatus(USART1, USART_IT_RXNE) == SET)
    {
        UART1_rx_dataframe();
        USART_ClearITPendingBit(USART1, USART_IT_RXNE);
    }
}

void USART2_IRQHandler(void)
{
    if (USART_GetITStatus(USART2, USART_IT_RXNE) == SET)
    {
        UART2_rx_dataframe();
        USART_ClearITPendingBit(USART2, USART_IT_RXNE);
    }
}

void USART3_IRQHandler(void)
{
    if (USART_GetITStatus(USART3, USART_IT_RXNE) == SET)
    {
        UART3_rx_dataframe();
        USART_ClearITPendingBit(USART3, USART_IT_RXNE);
    }
}

// 简化的串口2和串口3数据帧处理函数
void UART2_rx_dataframe()
{
    USART2_RxData = USART_ReceiveData(USART2);
    if (USART2_RxData == MY_UART2_HEADER)
    {
        uart2_rx_pt = 0;
        uart2_rx_buf[uart2_rx_pt++] = USART2_RxData;
    }
    else if (USART2_RxData == MY_UART2_TAIL && uart2_rx_pt > 0)
    {
        uart2_rx_buf[uart2_rx_pt++] = USART2_RxData;
        uart2_rx_pt = 0;
    }
    else if (uart2_rx_pt < RX_BUF2_SIZE - 1)
    {
        uart2_rx_buf[uart2_rx_pt++] = USART2_RxData;
    }
    else
    {
        uart2_rx_pt = 0;
    }
}

void UART3_rx_dataframe()
{
    USART3_RxData = USART_ReceiveData(USART3);
    if (USART3_RxData == MY_UART3_HEADER)
    {
        uart3_rx_pt = 0;
        uart3_rx_buf[uart3_rx_pt++] = USART3_RxData;
    }
    else if (USART3_RxData == MY_UART3_TAIL && uart3_rx_pt > 0)
    {
        uart3_rx_buf[uart3_rx_pt++] = USART3_RxData;
        uart3_rx_pt = 0;
    }
    else if (uart3_rx_pt < RX_BUF3_SIZE - 1)
    {
        uart3_rx_buf[uart3_rx_pt++] = USART3_RxData;
    }
    else
    {
        uart3_rx_pt = 0;
    }
}
