# 部分矩形检测功能测试脚本
# 用于验证优化后的部分矩形检测算法

import time
import math

# 模拟测试数据和函数
class MockImage:
    def __init__(self, width=320, height=240):
        self.w = width
        self.h = height
        
    def width(self):
        return self.w
        
    def height(self):
        return self.h

class MockBlob:
    def __init__(self, x, y, w, h, pixels):
        self.x = x
        self.y = y
        self.w = w
        self.h = h
        self.pixel_count = pixels
        
    def rect(self):
        return (self.x, self.y, self.w, self.h)
        
    def pixels(self):
        return self.pixel_count

# 从主程序复制的关键函数
RECT_WIDTH = 210
RECT_HEIGHT = 95
BORDER_TOLERANCE = 5
PARTIAL_MIN_AREA = 50
MAX_AREA = 100000

def is_potential_partial_rectangle(blob, img_width, img_height):
    """判断blob是否可能是部分矩形"""
    x, y, w, h = blob.rect()
    
    # 如果blob接触边界，可能是部分矩形
    touches_border = (x <= BORDER_TOLERANCE or 
                     y <= BORDER_TOLERANCE or 
                     x + w >= img_width - BORDER_TOLERANCE or 
                     y + h >= img_height - BORDER_TOLERANCE)
    
    # 检查形状特征
    aspect_ratio = w / max(h, 1)
    area = blob.pixels()
    
    # 宽松的形状约束
    reasonable_aspect = 0.2 < aspect_ratio < 5.0
    reasonable_area = PARTIAL_MIN_AREA < area < MAX_AREA
    
    return touches_border and reasonable_aspect and reasonable_area

def estimate_center_from_blob(blob, img_width, img_height):
    """从blob估算完整矩形的中心"""
    x, y, w, h = blob.rect()
    blob_center_x = x + w/2
    blob_center_y = y + h/2
    
    # 根据blob在图像中的位置估算完整矩形中心
    estimated_x = blob_center_x
    estimated_y = blob_center_y
    
    # 如果blob接触左边界，估算中心向右偏移
    if x <= BORDER_TOLERANCE:
        estimated_x = blob_center_x + RECT_WIDTH * 0.3
    # 如果blob接触右边界，估算中心向左偏移  
    elif x + w >= img_width - BORDER_TOLERANCE:
        estimated_x = blob_center_x - RECT_WIDTH * 0.3
    
    # 如果blob接触上边界，估算中心向下偏移
    if y <= BORDER_TOLERANCE:
        estimated_y = blob_center_y + RECT_HEIGHT * 0.3
    # 如果blob接触下边界，估算中心向上偏移
    elif y + h >= img_height - BORDER_TOLERANCE:
        estimated_y = blob_center_y - RECT_HEIGHT * 0.3
    
    # 确保估算的中心在图像范围内
    estimated_x = max(RECT_WIDTH//4, min(img_width - RECT_WIDTH//4, estimated_x))
    estimated_y = max(RECT_HEIGHT//4, min(img_height - RECT_HEIGHT//4, estimated_y))
    
    return (estimated_x, estimated_y)

# 测试函数
def test_partial_rectangle_detection():
    """测试部分矩形检测功能"""
    print("=== 部分矩形检测测试 ===")
    
    img = MockImage(320, 240)
    
    # 测试用例：不同位置的部分矩形
    test_cases = [
        # (x, y, w, h, pixels, description)
        (0, 100, 50, 80, 2000, "左边界部分矩形"),
        (270, 80, 50, 100, 2500, "右边界部分矩形"),
        (100, 0, 80, 40, 1800, "上边界部分矩形"),
        (120, 200, 90, 40, 2200, "下边界部分矩形"),
        (0, 0, 60, 60, 1500, "左上角部分矩形"),
        (260, 180, 60, 60, 1600, "右下角部分矩形"),
        (150, 120, 40, 30, 800, "中心小矩形（不应检测）"),
        (10, 50, 300, 20, 3000, "水平长条（边界）"),
    ]
    
    print(f"图像尺寸: {img.width()}x{img.height()}")
    print(f"边界容忍度: {BORDER_TOLERANCE}像素")
    print()
    
    for i, (x, y, w, h, pixels, desc) in enumerate(test_cases):
        blob = MockBlob(x, y, w, h, pixels)
        
        # 测试是否被识别为潜在部分矩形
        is_partial = is_potential_partial_rectangle(blob, img.width(), img.height())
        
        if is_partial:
            # 估算完整矩形中心
            estimated_center = estimate_center_from_blob(blob, img.width(), img.height())
            
            print(f"测试 {i+1}: {desc}")
            print(f"  Blob位置: ({x}, {y}) 尺寸: {w}x{h} 像素: {pixels}")
            print(f"  识别结果: 部分矩形 ✓")
            print(f"  估算中心: ({estimated_center[0]:.1f}, {estimated_center[1]:.1f})")
            
            # 计算偏移量
            blob_center = (x + w/2, y + h/2)
            offset_x = estimated_center[0] - blob_center[0]
            offset_y = estimated_center[1] - blob_center[1]
            print(f"  中心偏移: ({offset_x:.1f}, {offset_y:.1f})")
        else:
            print(f"测试 {i+1}: {desc}")
            print(f"  Blob位置: ({x}, {y}) 尺寸: {w}x{h} 像素: {pixels}")
            print(f"  识别结果: 非部分矩形 ✗")
        
        print()

def test_center_estimation_accuracy():
    """测试中心估算准确性"""
    print("=== 中心估算准确性测试 ===")
    
    img = MockImage(320, 240)
    
    # 模拟真实矩形被部分遮挡的情况
    real_center = (160, 120)  # 真实矩形中心
    real_rect_w = 100
    real_rect_h = 60
    
    # 模拟不同遮挡情况
    occlusion_cases = [
        # (visible_x, visible_y, visible_w, visible_h, description)
        (160, 120, 50, 60, "右半部分可见"),
        (110, 120, 50, 60, "左半部分可见"),
        (110, 120, 100, 30, "上半部分可见"),
        (110, 150, 100, 30, "下半部分可见"),
        (110, 120, 50, 30, "左上四分之一可见"),
    ]
    
    print(f"真实矩形中心: {real_center}")
    print(f"真实矩形尺寸: {real_rect_w}x{real_rect_h}")
    print()
    
    total_error = 0
    valid_tests = 0
    
    for i, (vx, vy, vw, vh, desc) in enumerate(occlusion_cases):
        blob = MockBlob(vx, vy, vw, vh, vw * vh)
        
        if is_potential_partial_rectangle(blob, img.width(), img.height()):
            estimated_center = estimate_center_from_blob(blob, img.width(), img.height())
            
            # 计算估算误差
            error_x = abs(estimated_center[0] - real_center[0])
            error_y = abs(estimated_center[1] - real_center[1])
            total_error_distance = math.sqrt(error_x**2 + error_y**2)
            
            print(f"测试 {i+1}: {desc}")
            print(f"  可见部分: ({vx}, {vy}) {vw}x{vh}")
            print(f"  估算中心: ({estimated_center[0]:.1f}, {estimated_center[1]:.1f})")
            print(f"  估算误差: ({error_x:.1f}, {error_y:.1f}) 距离: {total_error_distance:.1f}")
            
            total_error += total_error_distance
            valid_tests += 1
        else:
            print(f"测试 {i+1}: {desc} - 未被识别为部分矩形")
        
        print()
    
    if valid_tests > 0:
        avg_error = total_error / valid_tests
        print(f"平均估算误差: {avg_error:.1f} 像素")
        
        if avg_error < 20:
            print("✓ 估算精度良好")
        elif avg_error < 40:
            print("⚠ 估算精度一般")
        else:
            print("✗ 估算精度较差")

def test_edge_cases():
    """测试边界情况"""
    print("=== 边界情况测试 ===")
    
    img = MockImage(320, 240)
    
    edge_cases = [
        (0, 0, 10, 10, 100, "极小边角"),
        (0, 0, 320, 240, 76800, "整个图像"),
        (315, 235, 5, 5, 25, "右下角极小"),
        (-5, 100, 20, 50, 500, "超出左边界"),
        (310, 100, 20, 50, 500, "超出右边界"),
        (100, -5, 50, 20, 500, "超出上边界"),
        (100, 235, 50, 20, 500, "超出下边界"),
    ]
    
    for i, (x, y, w, h, pixels, desc) in enumerate(edge_cases):
        blob = MockBlob(x, y, w, h, pixels)
        
        try:
            is_partial = is_potential_partial_rectangle(blob, img.width(), img.height())
            
            if is_partial:
                estimated_center = estimate_center_from_blob(blob, img.width(), img.height())
                print(f"边界测试 {i+1}: {desc} ✓")
                print(f"  估算中心: ({estimated_center[0]:.1f}, {estimated_center[1]:.1f})")
            else:
                print(f"边界测试 {i+1}: {desc} - 未识别")
        except Exception as e:
            print(f"边界测试 {i+1}: {desc} - 错误: {e}")
        
        print()

def run_all_tests():
    """运行所有测试"""
    print("部分矩形检测算法测试")
    print("=" * 50)
    
    test_partial_rectangle_detection()
    test_center_estimation_accuracy()
    test_edge_cases()
    
    print("所有测试完成！")
    print("\n算法特点:")
    print("1. 能够检测接触图像边界的形状")
    print("2. 根据可见部分估算完整矩形中心")
    print("3. 对边界情况有良好的容错性")
    print("4. 适用于目标部分超出视野的场景")

if __name__ == "__main__":
    run_all_tests()
