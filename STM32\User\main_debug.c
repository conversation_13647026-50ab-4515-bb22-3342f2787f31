#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "nvic.h"
#include "usart.h"

// 调试用的LED或其他指示
void Debug_Init(void);
void Debug_ToggleLED(void);

int main(void)
{
    USART_INIT(); // 初始化串口
    NVIC_INIT();  // 配置NVIC优先级分组及使能串口接收中断
    Debug_Init(); // 初始化调试功能
    
    float pan_raw = 0;     // 云台水平角度（来自上位机）
    float tilt_raw = 0;    // 云台垂直角度（来自上位机）
    
    int pan_command = 0;   // 云台水平角度控制值
    int tilt_command = 0;  // 云台垂直角度控制值
    
    // 调试计数器
    uint32_t debug_counter = 0;
    uint32_t x_command_count = 0;
    uint32_t y_command_count = 0;
    
    while (1)
    {    
        debug_counter++;
        
        // 修复数据转换比例 - 使X轴和Y轴使用相同的比例
        pan_raw = (float)(pan - 1000) / 10.0f;   // 改为与Y轴相同的比例
        tilt_raw = (float)(tilt - 1000) / 10.0f; // 保持原有比例
        
        pan_command = (int)(pan_raw);
        tilt_command = (int)(tilt_raw);
        
        // X轴控制（水平轴）
        if(stepmotorx_Flag)
        {
            x_command_count++;
            
            // 调试输出 - 通过串口1发送调试信息
            if(debug_counter % 100 == 0) // 每100次循环输出一次
            {
                char debug_msg[50];
                sprintf(debug_msg, "X: raw=%.2f, cmd=%d\r\n", pan_raw, pan_command);
                USART_SendString(USART1, debug_msg);
            }
            
            if(pan_command > 0) // 需要右移动
            {
                // 增加步进电机速度，确保X轴能够响应
                StepperPacket_Fill(&stepper_packet_2, 0x01, 0x02, 0x01, 0x20, pan_command, 10); // 速度从5改为10
            }
            else if(pan_command < 0) // 需要左移动
            {
                StepperPacket_Fill(&stepper_packet_2, 0x01, 0x02, 0x00, 0x20, -pan_command, 10); // 速度从5改为10
            }
            
            // 确保数据包发送
            USART2_SendStepperPacket(&stepper_packet_2);
            
            // 清除标志
            stepmotorx_Flag = 0;
            
            // 调试指示
            Debug_ToggleLED();
        }
        
        // Y轴控制（垂直轴）
        if(stepmotory_Flag)
        {
            y_command_count++;
            
            // 调试输出
            if(debug_counter % 100 == 0)
            {
                char debug_msg[50];
                sprintf(debug_msg, "Y: raw=%.2f, cmd=%d\r\n", tilt_raw, tilt_command);
                USART_SendString(USART1, debug_msg);
            }
            
            if(tilt_command > 0) // 需要上移动
            {
                StepperPacket_Fill(&stepper_packet_3, 0x02, 0x02, 0x01, 0x20, tilt_command, 10);
            }
            else if(tilt_command < 0) // 需要下移动
            {
                StepperPacket_Fill(&stepper_packet_3, 0x02, 0x02, 0x00, 0x20, -tilt_command, 10);
            }    
            
            USART3_SendStepperPacket(&stepper_packet_3);
            stepmotory_Flag = 0;
        }
        
        // 定期输出统计信息
        if(debug_counter % 10000 == 0)
        {
            char stats_msg[100];
            sprintf(stats_msg, "Stats: X_cmds=%lu, Y_cmds=%lu, pan=%d, tilt=%d\r\n", 
                   x_command_count, y_command_count, (int)pan, (int)tilt);
            USART_SendString(USART1, stats_msg);
        }
        
        Delay_ms(1); // 1ms延时
    }
}

// 简单的调试功能实现
void Debug_Init(void)
{
    // 如果有LED，可以初始化GPIO
    // 这里省略具体实现
}

void Debug_ToggleLED(void)
{
    // 切换LED状态用于指示X轴命令执行
    // 这里省略具体实现
}
