# K230增强矩形追踪系统配置文件

# ========================= PID控制器参数 =========================
# 水平轴PID参数
PID_X_KP = 3.0              # 比例系数 - 控制响应速度
PID_X_KI = 0.15             # 积分系数 - 消除稳态误差  
PID_X_KD = 1.0              # 微分系数 - 减少超调
PID_X_MAX_OUTPUT = 25       # 最大输出限制
PID_X_INTEGRAL_LIMIT = 25   # 积分限制

# 垂直轴PID参数
PID_Y_KP = 3.0              # 比例系数
PID_Y_KI = 0.12             # 积分系数
PID_Y_KD = 1.0              # 微分系数
PID_Y_MAX_OUTPUT = 20       # 最大输出限制
PID_Y_INTEGRAL_LIMIT = 20   # 积分限制

# ========================= 目标稳定性参数 =========================
TARGET_MAX_HISTORY = 5          # 历史帧数 (3-10)
TARGET_STABILITY_THRESHOLD = 5  # 稳定性阈值 (3-15)
TARGET_MAX_LOST_FRAMES = 10     # 最大丢失帧数

# ========================= 云台控制参数 =========================
GIMBAL_DEAD_ZONE = 1.5          # 死区大小 (0.5-3.0)
GIMBAL_MIN_SEND_INTERVAL = 80   # 最小发送间隔(ms) (50-150)

# ========================= 增强矩形检测参数 =========================
# 完整矩形检测参数
MIN_RECT_AREA = 800                # 最小矩形面积 (500-1500)
MAX_RECT_AREA = 50000              # 最大矩形面积 (30000-80000)
MIN_ASPECT_RATIO = 1.5             # 最小宽高比 (1.2-2.0)
MAX_ASPECT_RATIO = 4.0             # 最大宽高比 (3.0-5.0)
MIN_EDGE_LENGTH = 20               # 最小边长 (15-30)
ANGLE_TOLERANCE = 15               # 角度容忍度 (10-20度)
CORNER_ANGLE_THRESHOLD = 70        # 角点角度阈值 (60-80度)

# 部分矩形检测参数
ENABLE_PARTIAL_DETECTION = True    # 是否启用部分矩形检测
PARTIAL_MIN_AREA = 200             # 部分矩形最小面积 (100-500)
BORDER_TOLERANCE = 8               # 边界容忍度像素 (5-15)
PARTIAL_CONFIDENCE_THRESHOLD = 50  # 部分检测置信度阈值 (40-70)

# 矩形验证参数
EDGE_SIMILARITY_THRESHOLD = 0.7    # 边长相似性阈值 (0.6-0.9)
MIN_RIGHT_ANGLES = 3               # 最少直角数量 (2-4)
CONFIDENCE_THRESHOLD = 50          # 最低置信度阈值 (40-80)

# ========================= 图像处理参数 =========================
# 灰度阈值
GRAY_THRESHOLD_LOW = 0             # 灰度下限 (0-50)
GRAY_THRESHOLD_HIGH = 100          # 灰度上限 (80-150)

# 边缘检测参数
EDGE_THRESHOLD_LOW = 30            # 边缘检测低阈值 (20-50)
EDGE_THRESHOLD_HIGH = 100          # 边缘检测高阈值 (80-150)

# 轮廓检测参数
CONTOUR_AREA_THRESHOLD = 200       # 轮廓面积阈值 (100-500)
CONTOUR_PIXELS_THRESHOLD = 150     # 轮廓像素阈值 (100-300)
CONTOUR_APPROX_EPSILON = 0.03      # 轮廓拟合精度 (0.02-0.05)

# 线段检测参数
LINE_THRESHOLD = 800               # 线段检测阈值 (500-1500)
LINE_THETA_MARGIN = 30             # 角度容差 (20-40)
LINE_RHO_MARGIN = 30               # 距离容差 (20-40)
LINE_MIN_LENGTH = 30               # 最小线段长度 (20-50)

# ========================= 显示和调试参数 =========================
SHOW_DEBUG_INFO = True             # 是否显示调试信息
SHOW_CONFIDENCE = True             # 是否显示置信度
SHOW_DETECTION_TYPE = True         # 是否显示检测类型
DEBUG_PRINT_INTERVAL = 20          # 调试信息打印间隔(帧)

# 颜色定义
COLOR_COMPLETE_RECT = (0, 255, 0)      # 完整矩形颜色 - 绿色
COLOR_PARTIAL_RECT = (255, 165, 0)     # 部分矩形颜色 - 橙色
COLOR_TARGET_CENTER = (255, 255, 0)    # 目标中心颜色 - 黄色
COLOR_IMAGE_CENTER = (255, 0, 255)     # 图像中心颜色 - 紫色
COLOR_TRACKING = (0, 255, 0)           # 追踪状态颜色 - 绿色
COLOR_UNSTABLE = (255, 255, 0)         # 不稳定状态颜色 - 黄色
COLOR_NO_TARGET = (255, 0, 0)          # 无目标状态颜色 - 红色

# ========================= 摄像头参数 =========================
CAMERA_WIDTH = 320                 # 摄像头宽度
CAMERA_HEIGHT = 240                # 摄像头高度
CAMERA_FPS_TARGET = 30             # 目标帧率

# ========================= 串口通信参数 =========================
UART_BAUDRATE = 115200             # 串口波特率
UART_TIMEOUT = 1000                # 串口超时时间(ms)

# ========================= 参数验证函数 =========================
def validate_enhanced_config():
    """验证增强配置参数的有效性"""
    errors = []
    
    # 验证PID参数
    if not (0.1 <= PID_X_KP <= 10.0):
        errors.append("PID_X_KP应在0.1-10.0范围内")
    if not (0.0 <= PID_X_KI <= 2.0):
        errors.append("PID_X_KI应在0.0-2.0范围内")
    if not (0.0 <= PID_X_KD <= 5.0):
        errors.append("PID_X_KD应在0.0-5.0范围内")
        
    # 验证矩形检测参数
    if not (500 <= MIN_RECT_AREA <= 2000):
        errors.append("MIN_RECT_AREA应在500-2000范围内")
    if not (20000 <= MAX_RECT_AREA <= 100000):
        errors.append("MAX_RECT_AREA应在20000-100000范围内")
    if not (1.0 <= MIN_ASPECT_RATIO <= 3.0):
        errors.append("MIN_ASPECT_RATIO应在1.0-3.0范围内")
    if not (2.0 <= MAX_ASPECT_RATIO <= 6.0):
        errors.append("MAX_ASPECT_RATIO应在2.0-6.0范围内")
        
    # 验证稳定性参数
    if not (3 <= TARGET_MAX_HISTORY <= 15):
        errors.append("TARGET_MAX_HISTORY应在3-15范围内")
    if not (2 <= TARGET_STABILITY_THRESHOLD <= 30):
        errors.append("TARGET_STABILITY_THRESHOLD应在2-30范围内")
        
    # 验证云台参数
    if not (0.3 <= GIMBAL_DEAD_ZONE <= 5.0):
        errors.append("GIMBAL_DEAD_ZONE应在0.3-5.0范围内")
    if not (20 <= GIMBAL_MIN_SEND_INTERVAL <= 200):
        errors.append("GIMBAL_MIN_SEND_INTERVAL应在20-200范围内")
        
    return errors

# ========================= 配置加载函数 =========================
def get_enhanced_pid_x_config():
    """获取增强水平轴PID配置"""
    return {
        'kp': PID_X_KP,
        'ki': PID_X_KI,
        'kd': PID_X_KD,
        'max_output': PID_X_MAX_OUTPUT,
        'integral_limit': PID_X_INTEGRAL_LIMIT
    }

def get_enhanced_pid_y_config():
    """获取增强垂直轴PID配置"""
    return {
        'kp': PID_Y_KP,
        'ki': PID_Y_KI,
        'kd': PID_Y_KD,
        'max_output': PID_Y_MAX_OUTPUT,
        'integral_limit': PID_Y_INTEGRAL_LIMIT
    }

def get_enhanced_detection_config():
    """获取增强检测参数配置"""
    return {
        'min_rect_area': MIN_RECT_AREA,
        'max_rect_area': MAX_RECT_AREA,
        'min_aspect_ratio': MIN_ASPECT_RATIO,
        'max_aspect_ratio': MAX_ASPECT_RATIO,
        'min_edge_length': MIN_EDGE_LENGTH,
        'angle_tolerance': ANGLE_TOLERANCE,
        'corner_angle_threshold': CORNER_ANGLE_THRESHOLD,
        'edge_similarity_threshold': EDGE_SIMILARITY_THRESHOLD,
        'min_right_angles': MIN_RIGHT_ANGLES,
        'confidence_threshold': CONFIDENCE_THRESHOLD
    }

def get_partial_detection_config():
    """获取部分检测参数配置"""
    return {
        'enable_partial_detection': ENABLE_PARTIAL_DETECTION,
        'partial_min_area': PARTIAL_MIN_AREA,
        'border_tolerance': BORDER_TOLERANCE,
        'partial_confidence_threshold': PARTIAL_CONFIDENCE_THRESHOLD
    }

def get_image_processing_config():
    """获取图像处理参数配置"""
    return {
        'gray_threshold_low': GRAY_THRESHOLD_LOW,
        'gray_threshold_high': GRAY_THRESHOLD_HIGH,
        'edge_threshold_low': EDGE_THRESHOLD_LOW,
        'edge_threshold_high': EDGE_THRESHOLD_HIGH,
        'contour_area_threshold': CONTOUR_AREA_THRESHOLD,
        'contour_pixels_threshold': CONTOUR_PIXELS_THRESHOLD,
        'contour_approx_epsilon': CONTOUR_APPROX_EPSILON,
        'line_threshold': LINE_THRESHOLD,
        'line_theta_margin': LINE_THETA_MARGIN,
        'line_rho_margin': LINE_RHO_MARGIN,
        'line_min_length': LINE_MIN_LENGTH
    }

# ========================= 使用说明 =========================
"""
增强配置文件使用说明：

1. 矩形识别精度调整：
   - 提高MIN_RECT_AREA：减少小物体误识别
   - 降低MAX_RECT_AREA：避免识别过大物体
   - 调整MIN/MAX_ASPECT_RATIO：限制矩形形状
   - 增大ANGLE_TOLERANCE：允许更多角度偏差
   - 提高CONFIDENCE_THRESHOLD：只接受高置信度检测

2. 部分矩形检测调整：
   - 设置ENABLE_PARTIAL_DETECTION=False：禁用部分检测
   - 增大PARTIAL_MIN_AREA：减少小片段误检
   - 调整BORDER_TOLERANCE：改变边界敏感度
   - 提高PARTIAL_CONFIDENCE_THRESHOLD：提高部分检测要求

3. 追踪性能调整：
   - 增大TARGET_MAX_HISTORY：提高稳定性但降低响应
   - 减小TARGET_STABILITY_THRESHOLD：更容易进入稳定状态
   - 调整GIMBAL_DEAD_ZONE：平衡精度和稳定性

4. 调试模式：
   - 设置SHOW_DEBUG_INFO=True：显示详细调试信息
   - 调整DEBUG_PRINT_INTERVAL：控制输出频率

推荐调试流程：
1. 先调整完整矩形检测参数
2. 再启用和调整部分矩形检测
3. 最后优化追踪和控制参数
"""
