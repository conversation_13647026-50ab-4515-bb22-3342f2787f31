# K230矩形追踪云台系统优化

## 项目概述

本项目是对K230识别矩形并通过串口控制STM32驱动步进电机云台进行目标追踪系统的全面优化。优化后的系统具有更好的稳定性、准确性和抗干扰能力。

## 系统架构

```
K230 (矩形识别) → 串口通信 → STM32 (步进电机控制) → 云台运动
```

## 主要优化内容

### 1. K230端优化
- **改进的PID控制器**：添加滤波、积分限幅、输出限幅
- **目标稳定性检测**：历史数据跟踪、稳定性判断、加权平均
- **云台控制优化**：死区处理、频率控制、指令限幅
- **坐标系统修正**：正确的误差计算和视野中心定义

### 2. STM32端优化
- **平滑控制**：使用平滑因子减少突变
- **死区处理**：小于阈值的指令不执行
- **速度自适应**：根据指令大小自动调整电机速度
- **数据验证**：检查接收数据的有效性和变化

### 3. 配置系统
- **参数配置文件**：方便调整各种参数
- **参数验证**：自动检查参数有效性
- **默认参数**：提供合理的默认配置

## 文件结构

```
day_0/
├── 矩形识别(1)(1).py          # 优化后的K230主程序
├── config.py                  # 配置参数文件
├── test_system.py             # 系统测试脚本
├── 优化说明和使用指南.md       # 详细使用说明
├── README.md                  # 本文件
└── STM32/
    ├── User/
    │   └── main_optimized.c    # 优化后的STM32主程序
    └── Hardware/
        └── usart_optimized.c   # 优化后的串口处理
```

## 快速开始

### 1. 部署K230代码
1. 将 `矩形识别(1)(1).py` 和 `config.py` 复制到K230设备
2. 根据需要修改 `config.py` 中的参数
3. 运行主程序

### 2. 部署STM32代码
1. 在Keil MDK中打开STM32项目
2. 替换 `main.c` 为 `main_optimized.c`
3. 替换 `usart.c` 为 `usart_optimized.c`
4. 编译并烧录到STM32

### 3. 系统测试
运行 `test_system.py` 进行系统功能验证：
```bash
python test_system.py
```

## 参数调整指南

### 基本调整原则
- **追踪太慢**：增大 `PID_X_KP` 和 `PID_Y_KP`
- **有震荡**：减小 `PID_X_KP`，增大 `PID_X_KD`
- **有稳态误差**：适当增大 `PID_X_KI`
- **目标容易跟丢**：减小 `TARGET_STABILITY_THRESHOLD`
- **抖动严重**：增大 `GIMBAL_DEAD_ZONE`

### 推荐参数组合

#### 快速响应模式
```python
PID_X_KP = 1.5
PID_X_KI = 0.08
PID_X_KD = 0.4
GIMBAL_DEAD_ZONE = 1.0
```

#### 平稳追踪模式
```python
PID_X_KP = 0.8
PID_X_KI = 0.03
PID_X_KD = 0.2
GIMBAL_DEAD_ZONE = 2.0
```

#### 抗干扰模式
```python
PID_X_KP = 1.0
PID_X_KI = 0.02
PID_X_KD = 0.3
TARGET_STABILITY_THRESHOLD = 12
GIMBAL_DEAD_ZONE = 2.5
```

## 性能改进

### 优化前的问题
- 追踪不稳定，容易震荡
- 目标丢失后难以重新锁定
- 步进电机运动不平滑
- 对环境干扰敏感

### 优化后的效果
- 追踪更加平滑稳定
- 更好的抗干扰能力
- 减少了不必要的电机运动
- 提高了目标锁定的可靠性

## 调试功能

### K230端调试信息
- 实时FPS显示
- 追踪状态显示（TRACKING/UNSTABLE/NO TARGET）
- 目标坐标和误差信息
- 检测到的矩形数量

### 控制台输出
- 配置参数加载状态
- PID参数信息
- 云台控制指令
- 系统状态信息

## 故障排除

### 常见问题

1. **云台不动**
   - 检查串口连接和波特率设置
   - 确认STM32程序正确烧录
   - 检查电机驱动器连接

2. **追踪不准确**
   - 调整PID参数
   - 检查死区设置
   - 验证坐标系统

3. **目标识别不稳定**
   - 调整矩形检测参数
   - 改善光照条件
   - 增大稳定性阈值

4. **系统卡顿**
   - 检查处理频率设置
   - 减少通信负载
   - 优化算法参数

### 调试步骤
1. 运行测试脚本验证各组件功能
2. 检查配置参数是否合理
3. 观察调试信息定位问题
4. 逐步调整参数优化性能

## 技术特性

- **实时性能**：优化后的算法保持高帧率
- **稳定性**：多层滤波和稳定性检测
- **可配置性**：丰富的参数配置选项
- **可扩展性**：模块化设计便于功能扩展

## 贡献

如果您发现问题或有改进建议，请：
1. 详细描述问题现象
2. 提供相关的参数配置
3. 包含调试信息和日志

## 许可证

本项目仅供学习和研究使用。

---

**注意**：使用前请仔细阅读 `优化说明和使用指南.md` 获取详细的使用说明和参数调整指导。
