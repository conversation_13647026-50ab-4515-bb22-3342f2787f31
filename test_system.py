# K230矩形追踪系统测试脚本
# 用于验证优化后的系统性能

import time
import math

# 模拟测试数据
class MockSensor:
    def __init__(self):
        self.width_val = 320
        self.height_val = 240
        
    def width(self):
        return self.width_val
        
    def height(self):
        return self.height_val

# 模拟时间函数
def ticks_ms():
    return int(time.time() * 1000)

# 导入优化后的类（需要从主程序中复制）
class ImprovedPID:
    def __init__(self, kp=0.8, ki=0.02, kd=0.15, max_output=30, integral_limit=20):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.max_output = max_output
        self.integral_limit = integral_limit
        self.prev_error = 0
        self.integral = 0
        self.prev_time = ticks_ms()
        self.error_history = []
        self.max_history = 3

    def compute(self, error):
        current_time = ticks_ms()
        dt = (current_time - self.prev_time) / 1000.0
        if dt <= 0: 
            dt = 0.01
        
        # 误差滤波
        self.error_history.append(error)
        if len(self.error_history) > self.max_history:
            self.error_history.pop(0)
        filtered_error = sum(self.error_history) / len(self.error_history)

        # 积分项计算
        self.integral += filtered_error * dt
        if self.integral > self.integral_limit: 
            self.integral = self.integral_limit
        elif self.integral < -self.integral_limit: 
            self.integral = -self.integral_limit

        # 微分项计算
        derivative = (filtered_error - self.prev_error) / dt

        # PID输出计算
        output = self.kp * filtered_error + self.ki * self.integral + self.kd * derivative

        # 输出限幅
        if output > self.max_output: 
            output = self.max_output
        elif output < -self.max_output: 
            output = -self.max_output

        self.prev_error = filtered_error
        self.prev_time = current_time
        return output

    def reset(self):
        self.prev_error = 0
        self.integral = 0
        self.error_history = []

class TargetStabilizer:
    def __init__(self, max_history=5, stability_threshold=10):
        self.center_history = []
        self.max_history = max_history
        self.stability_threshold = stability_threshold
        self.stable_center = None
        self.lost_count = 0
        self.max_lost_frames = 10
        
    def update(self, center):
        if center is None:
            self.lost_count += 1
            if self.lost_count > self.max_lost_frames:
                self.stable_center = None
                self.center_history = []
            return self.stable_center
        
        self.lost_count = 0
        self.center_history.append(center)
        
        if len(self.center_history) > self.max_history:
            self.center_history.pop(0)
        
        if len(self.center_history) >= 3:
            x_coords = [c[0] for c in self.center_history]
            y_coords = [c[1] for c in self.center_history]
            
            x_mean = sum(x_coords) / len(x_coords)
            y_mean = sum(y_coords) / len(y_coords)
            
            x_var = sum((x - x_mean)**2 for x in x_coords) / len(x_coords)
            y_var = sum((y - y_mean)**2 for y in y_coords) / len(y_coords)
            
            if x_var < self.stability_threshold and y_var < self.stability_threshold:
                self.stable_center = (x_mean, y_mean)
            else:
                weights = [i+1 for i in range(len(self.center_history))]
                total_weight = sum(weights)
                
                weighted_x = sum(x_coords[i] * weights[i] for i in range(len(x_coords))) / total_weight
                weighted_y = sum(y_coords[i] * weights[i] for i in range(len(y_coords))) / total_weight
                
                self.stable_center = (weighted_x, weighted_y)
        else:
            self.stable_center = center
            
        return self.stable_center
    
    def is_stable(self):
        return len(self.center_history) >= 3 and self.stable_center is not None

class GimbalController:
    def __init__(self, dead_zone=2.0, min_send_interval=50):
        self.dead_zone = dead_zone
        self.min_send_interval = min_send_interval
        self.last_send_time = 0
        self.last_pan_cmd = 0
        self.last_tilt_cmd = 0
        
    def send_command(self, pan_cmd, tilt_cmd, rect_x=0, rect_y=0):
        current_time = ticks_ms()
        
        pan_cmd = float(pan_cmd) if pan_cmd is not None else 0.0
        tilt_cmd = float(tilt_cmd) if tilt_cmd is not None else 0.0

        pan_diff = abs(pan_cmd - self.last_pan_cmd)
        tilt_diff = abs(tilt_cmd - self.last_tilt_cmd)
        
        if pan_diff < self.dead_zone and tilt_diff < self.dead_zone:
            return False
        
        if current_time - self.last_send_time < self.min_send_interval:
            return False
        
        pan_cmd = max(-50, min(50, pan_cmd))
        tilt_cmd = max(-50, min(50, tilt_cmd))

        self.last_send_time = current_time
        self.last_pan_cmd = pan_cmd
        self.last_tilt_cmd = tilt_cmd
        
        print(f"云台指令: Pan={pan_cmd:.2f}, Tilt={tilt_cmd:.2f}, X={rect_x}, Y={rect_y}")
        return True

# 测试函数
def test_pid_controller():
    """测试PID控制器性能"""
    print("=== PID控制器测试 ===")
    
    pid = ImprovedPID(kp=1.2, ki=0.05, kd=0.3, max_output=25)
    
    # 模拟阶跃响应
    target = 0
    current = 50  # 初始误差50像素
    
    print("阶跃响应测试（目标误差=0，初始误差=50）:")
    for i in range(20):
        error = target - current
        output = pid.compute(error)
        current += output * 0.1  # 模拟系统响应
        
        print(f"步骤{i+1:2d}: 误差={error:6.2f}, 输出={output:6.2f}, 当前位置={current:6.2f}")
        
        if abs(error) < 1:
            print(f"在第{i+1}步达到稳定状态")
            break
    
    print()

def test_target_stabilizer():
    """测试目标稳定器"""
    print("=== 目标稳定器测试 ===")
    
    stabilizer = TargetStabilizer(max_history=5, stability_threshold=8)
    
    # 模拟噪声目标
    base_center = (160, 120)
    noise_centers = [
        (162, 118), (158, 122), (161, 119), (159, 121), (160, 120),
        (163, 117), (157, 123), (160, 120), (161, 119), (159, 121)
    ]
    
    print("噪声目标稳定化测试:")
    for i, center in enumerate(noise_centers):
        stable = stabilizer.update(center)
        is_stable = stabilizer.is_stable()
        
        print(f"帧{i+1:2d}: 原始=({center[0]:3d},{center[1]:3d}), "
              f"稳定=({stable[0]:6.1f},{stable[1]:6.1f}), "
              f"状态={'稳定' if is_stable else '不稳定'}")
    
    print()

def test_gimbal_controller():
    """测试云台控制器"""
    print("=== 云台控制器测试 ===")
    
    controller = GimbalController(dead_zone=1.5, min_send_interval=80)
    
    # 模拟控制指令
    commands = [
        (5.0, 3.0), (5.2, 3.1), (5.0, 3.0), (7.0, 5.0), (7.1, 5.1), (0.5, 0.3)
    ]
    
    print("死区和频率控制测试:")
    for i, (pan, tilt) in enumerate(commands):
        sent = controller.send_command(pan, tilt, 160, 120)
        status = "发送" if sent else "跳过"
        print(f"指令{i+1}: Pan={pan}, Tilt={tilt} -> {status}")
        time.sleep(0.05)  # 模拟时间间隔
    
    print()

def test_integrated_system():
    """测试集成系统"""
    print("=== 集成系统测试 ===")
    
    # 初始化组件
    sensor = MockSensor()
    pid_x = ImprovedPID(kp=1.2, ki=0.05, kd=0.3, max_output=25)
    pid_y = ImprovedPID(kp=1.0, ki=0.03, kd=0.25, max_output=20)
    stabilizer = TargetStabilizer(max_history=5, stability_threshold=8)
    controller = GimbalController(dead_zone=1.5, min_send_interval=80)
    
    # 模拟追踪场景
    image_center_x = sensor.width() / 2
    image_center_y = sensor.height() / 2
    
    # 模拟目标移动轨迹
    target_trajectory = [
        (180, 140), (185, 145), (190, 150), (195, 155), (200, 160),
        (195, 155), (190, 150), (185, 145), (180, 140), (175, 135)
    ]
    
    print("完整追踪测试:")
    print(f"视野中心: ({image_center_x}, {image_center_y})")
    
    for i, target_pos in enumerate(target_trajectory):
        # 目标稳定化
        stable_center = stabilizer.update(target_pos)
        
        if stable_center and stabilizer.is_stable():
            stable_cx, stable_cy = stable_center
            
            # 计算误差
            error_x = stable_cx - image_center_x
            error_y = stable_cy - image_center_y
            
            # PID控制
            pan_cmd = pid_x.compute(error_x)
            tilt_cmd = pid_y.compute(error_y)
            
            # 发送指令
            sent = controller.send_command(pan_cmd, tilt_cmd, int(stable_cx), int(stable_cy))
            
            print(f"帧{i+1:2d}: 目标=({target_pos[0]:3d},{target_pos[1]:3d}), "
                  f"稳定=({stable_cx:5.1f},{stable_cy:5.1f}), "
                  f"误差=({error_x:5.1f},{error_y:5.1f}), "
                  f"PID=({pan_cmd:5.1f},{tilt_cmd:5.1f}), "
                  f"发送={'是' if sent else '否'}")
        else:
            print(f"帧{i+1:2d}: 目标不稳定或丢失")
        
        time.sleep(0.1)  # 模拟帧间隔
    
    print()

def run_all_tests():
    """运行所有测试"""
    print("K230矩形追踪系统优化测试")
    print("=" * 50)
    
    test_pid_controller()
    test_target_stabilizer()
    test_gimbal_controller()
    test_integrated_system()
    
    print("所有测试完成！")
    print("\n使用建议:")
    print("1. 如果PID响应太慢，增大Kp值")
    print("2. 如果有震荡，减小Kp值，增大Kd值")
    print("3. 如果目标容易跟丢，减小stability_threshold")
    print("4. 如果抖动严重，增大dead_zone")

if __name__ == "__main__":
    run_all_tests()
