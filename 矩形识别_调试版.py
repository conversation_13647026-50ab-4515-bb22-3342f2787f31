import time, os, sys
import math
import cv_lite
import ulab.numpy as np
from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms
from machine import UART, FPIOA

# 简化配置
PID_X_KP, PID_X_KI, PID_X_KD = 3.0, 0.1, 1.0
PID_Y_KP, PID_Y_KI, PID_Y_KD = 3.0, 0.1, 1.0
TARGET_MAX_HISTORY, TARGET_STABILITY_THRESHOLD = 5, 5
GIMBAL_DEAD_ZONE, GIMBAL_MIN_SEND_INTERVAL = 0.5, 30  # 降低死区和发送间隔用于调试

# 1. 摄像头初始化
sensor.reset()
sensor.set_framesize(sensor.QVGA)
sensor.set_pixformat(sensor.RGB565)
sensor.run(1)

# 2. 显示器初始化
Display.init(Display.ST7701, width=800, height=480, to_ide=True)
lcd_width = Display.width()
lcd_height = Display.height()

# 3. 媒体管理器初始化
MediaManager.init()

# 4. 串口初始化
fpioa = FPIOA()
fpioa.set_function(10, FPIOA.UART2_TX)
fpioa.set_function(9, FPIOA.UART2_RX)
uart2 = UART(UART.UART2, 115200, 8, 1, 0, timeout=1000, read_buf_len=4096)

# 5. 调试用PID控制器
class DebugPID:
    def __init__(self, kp=1.0, ki=0.1, kd=0.3, max_output=30, name="PID"):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.max_output = max_output
        self.prev_error = 0
        self.integral = 0
        self.prev_time = ticks_ms()
        self.name = name
        self.output_history = []
        
    def compute(self, error):
        current_time = ticks_ms()
        dt = (current_time - self.prev_time) / 1000.0
        if dt <= 0: 
            dt = 0.01
        
        # 简单PID计算
        self.integral += error * dt
        if self.integral > 50: 
            self.integral = 50
        elif self.integral < -50: 
            self.integral = -50

        derivative = (error - self.prev_error) / dt
        output = self.kp * error + self.ki * self.integral + self.kd * derivative

        if output > self.max_output: 
            output = self.max_output
        elif output < -self.max_output: 
            output = -self.max_output

        # 记录输出历史用于调试
        self.output_history.append(output)
        if len(self.output_history) > 10:
            self.output_history.pop(0)

        self.prev_error = error
        self.prev_time = current_time
        
        # 调试输出
        print(self.name + " - Error: " + str(round(error, 2)) + ", Output: " + str(round(output, 2)))
        
        return output

    def reset(self):
        self.prev_error = 0
        self.integral = 0
        self.output_history = []

# 创建调试PID控制器
pid_x = DebugPID(kp=PID_X_KP, ki=PID_X_KI, kd=PID_X_KD, max_output=25, name="X-PID")
pid_y = DebugPID(kp=PID_Y_KP, ki=PID_Y_KI, kd=PID_Y_KD, max_output=20, name="Y-PID")

# 6. 调试用云台控制器
class DebugGimbalController:
    def __init__(self, dead_zone=0.5, min_send_interval=30):
        self.dead_zone = dead_zone
        self.min_send_interval = min_send_interval
        self.last_send_time = 0
        self.last_pan_cmd = 0
        self.last_tilt_cmd = 0
        self.send_count = 0
        self.x_send_count = 0
        self.y_send_count = 0
        
    def send_command(self, pan_cmd, tilt_cmd, rect_x=0, rect_y=0):
        try:
            current_time = ticks_ms()
            
            pan_cmd = float(pan_cmd) if pan_cmd is not None else 0.0
            tilt_cmd = float(tilt_cmd) if tilt_cmd is not None else 0.0
            rect_x = int(rect_x) if rect_x is not None else 0
            rect_y = int(rect_y) if rect_y is not None else 0

            pan_diff = abs(pan_cmd - self.last_pan_cmd)
            tilt_diff = abs(tilt_cmd - self.last_tilt_cmd)
            
            # 调试：强制发送命令，忽略死区（用于测试）
            force_send = True  # 设置为True来强制发送所有命令
            
            if not force_send and (pan_diff < self.dead_zone and tilt_diff < self.dead_zone):
                return False
            
            if not force_send and (current_time - self.last_send_time < self.min_send_interval):
                return False
            
            # 限制输出范围
            pan_cmd = max(-50, min(50, pan_cmd))
            tilt_cmd = max(-50, min(50, tilt_cmd))

            # 编码数据
            pan_int = int(pan_cmd * 10) + 1000
            tilt_int = int(tilt_cmd * 10) + 1000

            rx_order = [0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFE]
            rx_order[1] = (pan_int >> 8) & 0xFF
            rx_order[2] = pan_int & 0xFF
            rx_order[3] = (tilt_int >> 8) & 0xFF
            rx_order[4] = tilt_int & 0xFF
            rx_order[5] = (rect_x >> 8) & 0xFF
            rx_order[6] = rect_x & 0xFF
            rx_order[7] = (rect_y >> 8) & 0xFF
            rx_order[8] = rect_y & 0xFF

            uart2.write(bytes(rx_order))
            
            self.last_send_time = current_time
            self.last_pan_cmd = pan_cmd
            self.last_tilt_cmd = tilt_cmd
            self.send_count += 1
            
            # 统计X轴和Y轴命令
            if abs(pan_cmd) > 0.1:
                self.x_send_count += 1
            if abs(tilt_cmd) > 0.1:
                self.y_send_count += 1
            
            # 详细调试输出
            print("=== 云台指令 #" + str(self.send_count) + " ===")
            print("Pan: " + str(round(pan_cmd, 2)) + " -> " + str(pan_int))
            print("Tilt: " + str(round(tilt_cmd, 2)) + " -> " + str(tilt_int))
            print("Raw bytes: " + str([hex(b) for b in rx_order]))
            print("X_cmds: " + str(self.x_send_count) + ", Y_cmds: " + str(self.y_send_count))
            print("========================")
            
            return True
            
        except Exception as e:
            print("发送指令失败: " + str(e))
            return False

# 创建调试云台控制器
gimbal_controller = DebugGimbalController(dead_zone=GIMBAL_DEAD_ZONE, min_send_interval=GIMBAL_MIN_SEND_INTERVAL)

# 7. 简化的矩形检测
def is_valid_rect(corners):
    if len(corners) != 4:
        return False
    try:
        edge_lengths = []
        for i in range(4):
            x1, y1 = corners[i]
            x2, y2 = corners[(i+1) % 4]
            length = math.sqrt((x2-x1)**2 + (y2-y1)**2)
            edge_lengths.append(length)
        
        edge_lengths.sort()
        ratio1 = edge_lengths[1] / max(edge_lengths[0], 1)
        ratio2 = edge_lengths[3] / max(edge_lengths[2], 1)
        
        return ratio1 > 0.8 and ratio2 > 0.8
    except:
        return False

def calculate_center(corners):
    if len(corners) != 4:
        return None
    try:
        cx = sum(p[0] for p in corners) / 4
        cy = sum(p[1] for p in corners) / 4
        return (cx, cy)
    except:
        return None

# 8. 时钟初始化
clock = time.clock()
frame_count = 0

print("调试版矩形追踪系统启动")
print("强制发送模式：开启")
print("调试输出：详细")

# 9. 主循环
while True:
    clock.tick()
    img = sensor.snapshot()
    frame_count += 1
    
    # 1. 矩形检测
    rects = cv_lite.grayscale_find_rectangles_with_corners(
        img.to_grayscale(),
        threshold=(0, 100),
        area_threshold=100,
        pixels_threshold=100,
        approx_epsilon=0.04
    )
    
    # 2. 寻找最小矩形
    best_rect = None
    smallest_rect_corners = None
    min_area = float('inf')
    
    for rect in rects:
        x, y, w, h = rect[0], rect[1], rect[2], rect[3]
        corners = [
            (rect[4], rect[5]), (rect[6], rect[7]),
            (rect[8], rect[9]), (rect[10], rect[11])
        ]

        if is_valid_rect(corners):
            area = w * h
            if area < min_area:
                min_area = area
                smallest_rect_corners = corners
                best_rect = calculate_center(corners)

    # 3. 控制逻辑
    if best_rect and smallest_rect_corners:
        cx, cy = best_rect
        
        # 计算视野中心
        image_center_x = sensor.width() / 2   # 160
        image_center_y = sensor.height() / 2  # 120
        
        # 计算误差
        error_x = cx - image_center_x
        error_y = cy - image_center_y
        
        print("=== 帧 #" + str(frame_count) + " ===")
        print("目标中心: (" + str(round(cx, 1)) + ", " + str(round(cy, 1)) + ")")
        print("视野中心: (" + str(image_center_x) + ", " + str(image_center_y) + ")")
        print("误差: X=" + str(round(error_x, 1)) + ", Y=" + str(round(error_y, 1)))
        
        # PID控制
        pan_cmd = pid_x.compute(error_x)
        tilt_cmd = pid_y.compute(error_y)
        
        # 发送控制指令
        gimbal_controller.send_command(pan_cmd, tilt_cmd, int(cx), int(cy))
        
        # 绘制检测结果
        for i in range(4):
            x1, y1 = smallest_rect_corners[i]
            x2, y2 = smallest_rect_corners[(i+1) % 4]
            img.draw_line(int(x1), int(y1), int(x2), int(y2), color=(255, 0, 0), thickness=2)
        
        for p in smallest_rect_corners:
            img.draw_circle(int(p[0]), int(p[1]), 5, color=(0, 255, 0), thickness=2)
            
        # 绘制中心点和误差
        img.draw_circle(int(cx), int(cy), 8, color=(255, 255, 0), thickness=2)
        img.draw_circle(int(image_center_x), int(image_center_y), 4, color=(255, 0, 255), thickness=2)
        
        # 绘制误差向量
        img.draw_line(int(image_center_x), int(image_center_y), int(cx), int(cy), color=(0, 255, 255), thickness=1)
    else:
        pid_x.reset()
        pid_y.reset()
        print("=== 帧 #" + str(frame_count) + " ===")
        print("未检测到目标")

    # 4. 显示信息
    fps = clock.fps()
    img.draw_string_advanced(10, 10, 16, "DEBUG FPS: " + str(round(fps, 1)), color=(255, 255, 255))
    img.draw_string_advanced(10, 30, 16, "Frame: " + str(frame_count), color=(255, 255, 255))
    img.draw_string_advanced(10, 50, 16, "Rects: " + str(len(rects)), color=(255, 255, 255))
    img.draw_string_advanced(10, 70, 16, "X_cmds: " + str(gimbal_controller.x_send_count), color=(255, 255, 255))
    img.draw_string_advanced(10, 90, 16, "Y_cmds: " + str(gimbal_controller.y_send_count), color=(255, 255, 255))

    # 显示图像
    Display.show_image(img,
                      x=round((lcd_width-sensor.width())/2),
                      y=round((lcd_height-sensor.height())/2))

    # 每10帧输出一次统计
    if frame_count % 10 == 0:
        print("统计: 总帧数=" + str(frame_count) + ", X轴命令=" + str(gimbal_controller.x_send_count) + ", Y轴命令=" + str(gimbal_controller.y_send_count))
        
    time.sleep(0.1)  # 降低帧率便于观察调试信息
