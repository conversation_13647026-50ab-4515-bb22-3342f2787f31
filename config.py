# K230矩形追踪系统配置文件
# 修改此文件中的参数来调整系统性能

# ========================= PID控制器参数 =========================
# 水平轴PID参数
PID_X_KP = 1.2          # 比例系数 - 控制响应速度
PID_X_KI = 0.05         # 积分系数 - 消除稳态误差  
PID_X_KD = 0.3          # 微分系数 - 减少超调
PID_X_MAX_OUTPUT = 25   # 最大输出限制
PID_X_INTEGRAL_LIMIT = 15  # 积分限制

# 垂直轴PID参数
PID_Y_KP = 1.0          # 比例系数
PID_Y_KI = 0.03         # 积分系数
PID_Y_KD = 0.25         # 微分系数
PID_Y_MAX_OUTPUT = 20   # 最大输出限制
PID_Y_INTEGRAL_LIMIT = 12  # 积分限制

# ========================= 目标稳定性参数 =========================
TARGET_MAX_HISTORY = 5          # 历史帧数 (3-10)
TARGET_STABILITY_THRESHOLD = 8  # 稳定性阈值 (5-15)
TARGET_MAX_LOST_FRAMES = 10     # 最大丢失帧数

# ========================= 云台控制参数 =========================
GIMBAL_DEAD_ZONE = 1.5          # 死区大小 (0.5-3.0)
GIMBAL_MIN_SEND_INTERVAL = 80   # 最小发送间隔(ms) (50-150)

# ========================= 矩形检测参数 =========================
# Canny边缘检测参数
CANNY_THRESH1 = 50      # 低阈值 (30-80)
CANNY_THRESH2 = 150     # 高阈值 (100-200)

# 多边形拟合参数
APPROX_EPSILON = 0.04   # 拟合精度 (0.02-0.08)

# 面积和形状筛选参数
AREA_MIN_RATIO = 0.005  # 最小面积比例 (0.001-0.01)
MAX_ANGLE_COS = 0.3     # 角度余弦阈值 (0.1-0.5)
MIN_AREA = 100          # 最小面积
MAX_AREA = 100000       # 最大面积
MIN_ASPECT_RATIO = 0.3  # 最小宽高比
MAX_ASPECT_RATIO = 3.0  # 最大宽高比

# ========================= 部分矩形检测参数 =========================
ENABLE_PARTIAL_DETECTION = True    # 是否启用部分矩形检测
EDGE_MIN_LENGTH = 30               # 最小边缘长度 (20-50)
EDGE_MAX_GAP = 10                  # 边缘最大间隙 (5-20)
PARTIAL_MIN_AREA = 50              # 部分矩形最小面积 (30-100)
BORDER_TOLERANCE = 5               # 边界容忍度像素 (3-10)
PARTIAL_CONFIDENCE_THRESHOLD = 0.5  # 部分检测置信度阈值 (0.3-0.8)

# ========================= 显示和调试参数 =========================
SHOW_DEBUG_INFO = True          # 是否显示调试信息
SHOW_TARGET_HISTORY = True      # 是否显示目标历史轨迹
DEBUG_PRINT_INTERVAL = 10       # 调试信息打印间隔(帧)

# ========================= 摄像头参数 =========================
CAMERA_WIDTH = 320      # 摄像头宽度
CAMERA_HEIGHT = 240     # 摄像头高度
CAMERA_FPS_TARGET = 30  # 目标帧率

# ========================= 串口通信参数 =========================
UART_BAUDRATE = 115200  # 串口波特率
UART_TIMEOUT = 0.1      # 串口超时时间

# ========================= 高级参数 =========================
# PID滤波参数
PID_FILTER_HISTORY = 3  # PID滤波历史帧数

# 目标预测参数（预留）
ENABLE_TARGET_PREDICTION = False  # 是否启用目标预测
PREDICTION_FRAMES = 3             # 预测帧数

# 自适应参数调整（预留）
ENABLE_ADAPTIVE_PARAMS = False    # 是否启用自适应参数调整

# ========================= 参数验证函数 =========================
def validate_config():
    """验证配置参数的有效性"""
    errors = []
    
    # 验证PID参数
    if not (0.1 <= PID_X_KP <= 5.0):
        errors.append("PID_X_KP应在0.1-5.0范围内")
    if not (0.0 <= PID_X_KI <= 1.0):
        errors.append("PID_X_KI应在0.0-1.0范围内")
    if not (0.0 <= PID_X_KD <= 2.0):
        errors.append("PID_X_KD应在0.0-2.0范围内")
        
    # 验证稳定性参数
    if not (3 <= TARGET_MAX_HISTORY <= 10):
        errors.append("TARGET_MAX_HISTORY应在3-10范围内")
    if not (5 <= TARGET_STABILITY_THRESHOLD <= 20):
        errors.append("TARGET_STABILITY_THRESHOLD应在5-20范围内")
        
    # 验证云台参数
    if not (0.5 <= GIMBAL_DEAD_ZONE <= 5.0):
        errors.append("GIMBAL_DEAD_ZONE应在0.5-5.0范围内")
    if not (30 <= GIMBAL_MIN_SEND_INTERVAL <= 200):
        errors.append("GIMBAL_MIN_SEND_INTERVAL应在30-200范围内")
        
    return errors

# ========================= 配置加载函数 =========================
def get_pid_x_config():
    """获取水平轴PID配置"""
    return {
        'kp': PID_X_KP,
        'ki': PID_X_KI,
        'kd': PID_X_KD,
        'max_output': PID_X_MAX_OUTPUT,
        'integral_limit': PID_X_INTEGRAL_LIMIT
    }

def get_pid_y_config():
    """获取垂直轴PID配置"""
    return {
        'kp': PID_Y_KP,
        'ki': PID_Y_KI,
        'kd': PID_Y_KD,
        'max_output': PID_Y_MAX_OUTPUT,
        'integral_limit': PID_Y_INTEGRAL_LIMIT
    }

def get_target_stabilizer_config():
    """获取目标稳定器配置"""
    return {
        'max_history': TARGET_MAX_HISTORY,
        'stability_threshold': TARGET_STABILITY_THRESHOLD,
        'max_lost_frames': TARGET_MAX_LOST_FRAMES
    }

def get_gimbal_controller_config():
    """获取云台控制器配置"""
    return {
        'dead_zone': GIMBAL_DEAD_ZONE,
        'min_send_interval': GIMBAL_MIN_SEND_INTERVAL
    }

def get_detection_config():
    """获取检测参数配置"""
    return {
        'canny_thresh1': CANNY_THRESH1,
        'canny_thresh2': CANNY_THRESH2,
        'approx_epsilon': APPROX_EPSILON,
        'area_min_ratio': AREA_MIN_RATIO,
        'max_angle_cos': MAX_ANGLE_COS,
        'min_area': MIN_AREA,
        'max_area': MAX_AREA,
        'min_aspect_ratio': MIN_ASPECT_RATIO,
        'max_aspect_ratio': MAX_ASPECT_RATIO
    }

# ========================= 使用说明 =========================
"""
配置文件使用说明：

1. 基本调整原则：
   - 如果追踪太慢：增大PID_X_KP和PID_Y_KP
   - 如果有震荡：减小PID_X_KP，增大PID_X_KD
   - 如果有稳态误差：适当增大PID_X_KI
   - 如果目标容易跟丢：减小TARGET_STABILITY_THRESHOLD
   - 如果抖动严重：增大GIMBAL_DEAD_ZONE

2. 参数调整步骤：
   a) 先调整PID参数获得基本的追踪性能
   b) 再调整稳定性参数减少抖动
   c) 最后调整云台参数优化响应

3. 调试技巧：
   - 设置SHOW_DEBUG_INFO=True查看实时状态
   - 逐步调整参数，每次只改变一个参数
   - 在不同光照和背景条件下测试

4. 常用参数组合：
   - 快速响应：KP=1.5, KI=0.08, KD=0.4
   - 平稳追踪：KP=0.8, KI=0.03, KD=0.2
   - 抗干扰：增大DEAD_ZONE和STABILITY_THRESHOLD
"""
