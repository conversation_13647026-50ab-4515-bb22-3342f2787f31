# 部分矩形检测功能说明

## 功能概述

新增的部分矩形检测功能解决了原系统只能检测完整矩形的限制。当目标矩形部分超出视野边界时，系统仍能检测并追踪目标。

## 工作原理

### 1. 检测策略
系统采用双重检测策略：
- **优先检测**：完整矩形检测（原有算法）
- **备用检测**：部分矩形检测（新增算法）

### 2. 部分矩形检测流程

```
图像输入 → 边缘轮廓检测 → 边界接触判断 → 形状验证 → 中心估算 → 目标追踪
```

#### 步骤详解：

1. **边缘轮廓检测**
   - 使用更宽松的阈值检测所有可能的形状
   - 降低面积要求以捕获部分可见的目标

2. **边界接触判断**
   - 检查检测到的形状是否接触图像边界
   - 边界容忍度：5像素（可配置）

3. **形状验证**
   - 宽高比验证：0.2 < ratio < 5.0（比完整检测更宽松）
   - 面积验证：50 < area < 100000像素

4. **中心估算**
   - 根据可见部分的位置推断完整矩形的中心
   - 考虑矩形的标准尺寸进行偏移计算

## 核心算法

### 中心估算算法

```python
def estimate_center_from_blob(blob, img_width, img_height):
    x, y, w, h = blob.rect()
    blob_center_x = x + w/2
    blob_center_y = y + h/2
    
    estimated_x = blob_center_x
    estimated_y = blob_center_y
    
    # 根据边界位置调整估算中心
    if x <= BORDER_TOLERANCE:  # 接触左边界
        estimated_x = blob_center_x + RECT_WIDTH * 0.3
    elif x + w >= img_width - BORDER_TOLERANCE:  # 接触右边界
        estimated_x = blob_center_x - RECT_WIDTH * 0.3
    
    if y <= BORDER_TOLERANCE:  # 接触上边界
        estimated_y = blob_center_y + RECT_HEIGHT * 0.3
    elif y + h >= img_height - BORDER_TOLERANCE:  # 接触下边界
        estimated_y = blob_center_y - RECT_HEIGHT * 0.3
    
    return (estimated_x, estimated_y)
```

## 视觉反馈

### 颜色编码
- **红色边框 + 绿色角点**：完整矩形检测
- **橙色边框 + 黄色角点**：部分矩形检测

### 显示信息
- `Type: complete (1.0)`：完整矩形，置信度1.0
- `Type: edge_based (0.6)`：部分矩形，置信度0.6

## 配置参数

在 `config.py` 中可以调整以下参数：

```python
# 部分矩形检测参数
ENABLE_PARTIAL_DETECTION = True    # 是否启用部分矩形检测
PARTIAL_MIN_AREA = 50              # 部分矩形最小面积
BORDER_TOLERANCE = 5               # 边界容忍度像素
PARTIAL_CONFIDENCE_THRESHOLD = 0.5  # 部分检测置信度阈值
```

## 使用场景

### 1. 目标进入视野
当矩形目标从视野边缘进入时，系统能够：
- 检测到部分可见的矩形
- 估算完整矩形的中心位置
- 开始追踪控制

### 2. 目标离开视野
当矩形目标移动到视野边缘时，系统能够：
- 继续检测部分可见的矩形
- 保持追踪直到完全消失
- 平滑过渡追踪状态

### 3. 大目标追踪
当目标矩形较大，无法完全显示在视野内时：
- 系统能够检测可见部分
- 估算目标中心进行追踪
- 适应不同的遮挡情况

## 性能特点

### 优势
1. **扩展检测范围**：能检测部分可见的目标
2. **平滑追踪**：减少目标丢失的情况
3. **智能估算**：根据可见部分推断完整位置
4. **视觉反馈**：清晰区分检测类型

### 限制
1. **估算精度**：部分检测的精度低于完整检测
2. **置信度较低**：部分检测置信度通常为0.6-0.8
3. **形状依赖**：需要目标具有明显的矩形特征

## 调试和优化

### 参数调整建议

1. **如果检测过于敏感**（误检太多）：
   - 增大 `PARTIAL_MIN_AREA`
   - 减小 `BORDER_TOLERANCE`
   - 提高 `PARTIAL_CONFIDENCE_THRESHOLD`

2. **如果检测不够敏感**（漏检太多）：
   - 减小 `PARTIAL_MIN_AREA`
   - 增大 `BORDER_TOLERANCE`
   - 降低 `PARTIAL_CONFIDENCE_THRESHOLD`

3. **如果中心估算不准确**：
   - 调整估算偏移系数（代码中的0.3）
   - 根据实际矩形尺寸调整 `RECT_WIDTH` 和 `RECT_HEIGHT`

### 测试方法

运行测试脚本验证功能：
```bash
python test_partial_detection.py
```

测试内容包括：
- 不同位置的部分矩形检测
- 中心估算精度测试
- 边界情况处理测试

## 实际应用效果

### 改进前
- 目标部分超出视野时立即丢失
- 追踪中断，需要重新锁定
- 对大目标追踪效果差

### 改进后
- 能够检测和追踪部分可见的目标
- 追踪更加连续和稳定
- 适应各种目标尺寸和位置

## 注意事项

1. **优先级**：系统优先使用完整矩形检测，只有在检测不到完整矩形时才使用部分检测
2. **置信度**：部分检测的置信度较低，在某些应用中可能需要额外的验证
3. **环境要求**：部分检测对光照和背景对比度要求较高
4. **参数调整**：建议根据实际使用环境调整检测参数

## 总结

部分矩形检测功能显著提升了系统的鲁棒性和实用性，特别是在以下场景中：
- 目标进入/离开视野的过渡阶段
- 大目标无法完全显示的情况
- 需要连续追踪的应用场景

通过合理的参数配置和调试，该功能能够有效补充原有的完整矩形检测，提供更加稳定和可靠的目标追踪体验。
