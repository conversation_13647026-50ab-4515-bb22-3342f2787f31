import time, os, sys
import math
import cv_lite
import ulab.numpy as np
import sensor
import image
from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms
from machine import UART, FPIOA

# 导入配置文件
try:
    from config import *
    print("配置文件加载成功")
except ImportError:
    print("未找到config.py，使用默认参数")
    PID_X_KP, PID_X_KI, PID_X_KD = 3.0, 0.1, 1.0
    PID_Y_KP, PID_Y_KI, PID_Y_KD = 3.0, 0.1, 1.0
    TARGET_MAX_HISTORY, TARGET_STABILITY_THRESHOLD = 5, 5
    GIMBAL_DEAD_ZONE, GIMBAL_MIN_SEND_INTERVAL = 1.5, 80

# 1. 摄像头初始化
sensor.reset()
sensor.set_framesize(sensor.QVGA)  # 320x240
sensor.set_pixformat(sensor.RGB565)
sensor.run(1)

# 2. 显示器初始化
Display.init(Display.ST7701, width=800, height=480, to_ide=True)
lcd_width = Display.width()
lcd_height = Display.height()

# 3. 媒体管理器初始化
MediaManager.init()

# 4. 串口初始化
fpioa = FPIOA()
fpioa.set_function(10, FPIOA.UART2_TX)
fpioa.set_function(9, FPIOA.UART2_RX)
uart2 = UART(UART.UART2, 115200, 8, 1, 0, timeout=1000, read_buf_len=4096)

# 5. 颜色阈值和检测参数
PURPLE_THRESHOLD = (0, 100, -128, 127, -128, 127)
RECT_WIDTH = 210
RECT_HEIGHT = 95
BORDER_TOLERANCE = 5
PARTIAL_MIN_AREA = 50
MAX_AREA = 100000

# 6. 高精度PID控制器
class HighPrecisionPID:
    def __init__(self, kp=1.0, ki=0.2, kd=0.3, max_output=30, integral_limit=50):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.max_output = max_output
        self.integral_limit = integral_limit
        self.prev_error = 0
        self.integral = 0
        self.prev_time = ticks_ms()
        self.error_history = []
        self.max_history = 5
        
        # 稳态精度优化参数
        self.steady_state_threshold = 2.0
        self.steady_state_count = 0
        self.in_steady_state = False
        self.precision_mode = False

    def compute(self, error):
        current_time = ticks_ms()
        dt = (current_time - self.prev_time) / 1000.0
        if dt <= 0: 
            dt = 0.01
        
        # 误差滤波
        self.error_history.append(error)
        if len(self.error_history) > self.max_history:
            self.error_history.pop(0)
        filtered_error = sum(self.error_history) / len(self.error_history)

        # 检测是否进入稳态
        if abs(filtered_error) < self.steady_state_threshold:
            self.steady_state_count += 1
            if self.steady_state_count > 10:
                self.in_steady_state = True
                self.precision_mode = True
        else:
            self.steady_state_count = 0
            self.in_steady_state = False
            if abs(filtered_error) > self.steady_state_threshold * 2:
                self.precision_mode = False

        # 根据状态调整PID参数
        if self.precision_mode:
            effective_kp = self.kp * 0.6
            effective_ki = self.ki * 2.0
            effective_kd = self.kd * 1.2
        else:
            effective_kp = self.kp
            effective_ki = self.ki
            effective_kd = self.kd

        # 积分项计算
        self.integral += filtered_error * dt
        if not self.precision_mode:
            if self.integral > self.integral_limit: 
                self.integral = self.integral_limit
            elif self.integral < -self.integral_limit: 
                self.integral = -self.integral_limit

        # 微分项计算
        derivative = (filtered_error - self.prev_error) / dt

        # PID输出计算
        output = effective_kp * filtered_error + effective_ki * self.integral + effective_kd * derivative

        # 输出限幅
        if self.precision_mode:
            max_out = self.max_output * 0.3
            min_out = 0.5
            if abs(output) < min_out and abs(filtered_error) > 0.5:
                output = min_out if output >= 0 else -min_out
        else:
            max_out = self.max_output
            
        if output > max_out: 
            output = max_out
        elif output < -max_out: 
            output = -max_out

        self.prev_error = filtered_error
        self.prev_time = current_time
        return output

    def reset(self):
        self.prev_error = 0
        self.integral = 0
        self.error_history = []
        self.steady_state_count = 0
        self.in_steady_state = False
        self.precision_mode = False

    def get_status(self):
        return {
            'steady_state': self.in_steady_state,
            'precision_mode': self.precision_mode,
            'integral': self.integral,
            'steady_count': self.steady_state_count
        }

# 创建高精度PID控制器
pid_x = HighPrecisionPID(
    kp=PID_X_KP, 
    ki=max(PID_X_KI, 0.25),
    kd=PID_X_KD, 
    max_output=35,
    integral_limit=60
)
pid_y = HighPrecisionPID(
    kp=PID_Y_KP, 
    ki=max(PID_Y_KI, 0.22), 
    kd=PID_Y_KD, 
    max_output=30,
    integral_limit=50
)

# 7. 增强目标稳定器
class EnhancedTargetStabilizer:
    def __init__(self, max_history=8, stability_threshold=5):
        self.center_history = []
        self.max_history = max_history
        self.stability_threshold = stability_threshold
        self.stable_center = None
        self.lost_count = 0
        self.max_lost_frames = 15
        self.precision_center = None
        
    def update(self, center):
        if center is None:
            self.lost_count += 1
            if self.lost_count > self.max_lost_frames:
                self.stable_center = None
                self.precision_center = None
                self.center_history = []
            return self.stable_center
        
        self.lost_count = 0
        self.center_history.append(center)
        
        if len(self.center_history) > self.max_history:
            self.center_history.pop(0)
        
        if len(self.center_history) >= 5:
            x_coords = [c[0] for c in self.center_history]
            y_coords = [c[1] for c in self.center_history]
            
            weights = [i+1 for i in range(len(self.center_history))]
            total_weight = sum(weights)
            
            weighted_x = sum(x_coords[i] * weights[i] for i in range(len(x_coords))) / total_weight
            weighted_y = sum(y_coords[i] * weights[i] for i in range(len(y_coords))) / total_weight
            
            x_var = sum((x - weighted_x)**2 for x in x_coords[-5:]) / 5
            y_var = sum((y - weighted_y)**2 for y in y_coords[-5:]) / 5
            
            if x_var < self.stability_threshold and y_var < self.stability_threshold:
                self.precision_center = (weighted_x, weighted_y)
                self.stable_center = self.precision_center
            else:
                self.stable_center = (weighted_x, weighted_y)
                self.precision_center = None
        else:
            self.stable_center = center
            self.precision_center = None
            
        return self.stable_center
    
    def is_stable(self):
        return len(self.center_history) >= 5 and self.stable_center is not None
        
    def is_high_precision(self):
        return self.precision_center is not None

# 创建增强目标稳定器
target_stabilizer = EnhancedTargetStabilizer(max_history=8, stability_threshold=3)

# 8. 高精度云台控制器
class PrecisionGimbalController:
    def __init__(self, dead_zone=0.8, min_send_interval=60):
        self.dead_zone = dead_zone
        self.min_send_interval = min_send_interval
        self.last_send_time = 0
        self.last_pan_cmd = 0
        self.last_tilt_cmd = 0
        self.precision_mode = False
        
    def send_command(self, pan_cmd, tilt_cmd, rect_x=0, rect_y=0, precision_mode=False):
        try:
            current_time = ticks_ms()
            
            pan_cmd = float(pan_cmd) if pan_cmd is not None else 0.0
            tilt_cmd = float(tilt_cmd) if tilt_cmd is not None else 0.0
            rect_x = int(rect_x) if rect_x is not None else 0
            rect_y = int(rect_y) if rect_y is not None else 0

            effective_dead_zone = self.dead_zone * 0.3 if precision_mode else self.dead_zone
            effective_interval = self.min_send_interval * 0.7 if precision_mode else self.min_send_interval

            pan_diff = abs(pan_cmd - self.last_pan_cmd)
            tilt_diff = abs(tilt_cmd - self.last_tilt_cmd)
            
            if pan_diff < effective_dead_zone and tilt_diff < effective_dead_zone:
                return False
            
            if current_time - self.last_send_time < effective_interval:
                return False
            
            max_cmd = 60 if precision_mode else 50
            pan_cmd = max(-max_cmd, min(max_cmd, pan_cmd))
            tilt_cmd = max(-max_cmd, min(max_cmd, tilt_cmd))

            pan_int = int(pan_cmd * 10) + 1000
            tilt_int = int(tilt_cmd * 10) + 1000

            rx_order = [0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFE]
            rx_order[1] = (pan_int >> 8) & 0xFF
            rx_order[2] = pan_int & 0xFF
            rx_order[3] = (tilt_int >> 8) & 0xFF
            rx_order[4] = tilt_int & 0xFF
            rx_order[5] = (rect_x >> 8) & 0xFF
            rx_order[6] = rect_x & 0xFF
            rx_order[7] = (rect_y >> 8) & 0xFF
            rx_order[8] = rect_y & 0xFF

            uart2.write(bytes(rx_order))
            
            self.last_send_time = current_time
            self.last_pan_cmd = pan_cmd
            self.last_tilt_cmd = tilt_cmd
            self.precision_mode = precision_mode
            
            mode_str = "[精度]" if precision_mode else "[普通]"
            print("云台指令" + mode_str + ": Pan=" + str(round(pan_cmd, 2)) + ", Tilt=" + str(round(tilt_cmd, 2)))
            return True
            
        except:
            print("发送指令失败")
            return False

# 创建精度云台控制器
gimbal_controller = PrecisionGimbalController(dead_zone=0.8, min_send_interval=50)

# 9. 矩形验证函数
def is_valid_rect(corners):
    if len(corners) != 4:
        return False
    
    try:
        edge_lengths = []
        for i in range(4):
            x1, y1 = corners[i]
            x2, y2 = corners[(i+1) % 4]
            length = math.sqrt((x2-x1)**2 + (y2-y1)**2)
            edge_lengths.append(length)
        
        edge_lengths.sort()
        ratio1 = edge_lengths[1] / max(edge_lengths[0], 1)
        ratio2 = edge_lengths[3] / max(edge_lengths[2], 1)
        
        return ratio1 > 0.8 and ratio2 > 0.8
    except:
        return False

def calculate_center(corners):
    if len(corners) != 4:
        return None
    try:
        cx = sum(p[0] for p in corners) / 4
        cy = sum(p[1] for p in corners) / 4
        return (cx, cy)
    except:
        return None

# 10. 简化的边缘检测
def detect_edge_rectangles(img):
    edge_rects = []
    try:
        blobs = img.find_blobs(
            [(0, 80)],
            pixels_threshold=30,
            area_threshold=PARTIAL_MIN_AREA,
            merge=True
        )
        
        for blob in blobs:
            x, y, w, h = blob.rect()
            
            touches_left = x <= BORDER_TOLERANCE
            touches_right = x + w >= img.width() - BORDER_TOLERANCE
            touches_top = y <= BORDER_TOLERANCE
            touches_bottom = y + h >= img.height() - BORDER_TOLERANCE
            
            if touches_left or touches_right or touches_top or touches_bottom:
                blob_cx = x + w/2
                blob_cy = y + h/2
                
                est_cx = blob_cx
                est_cy = blob_cy
                
                if touches_left:
                    est_cx = blob_cx + 30
                elif touches_right:
                    est_cx = blob_cx - 30
                    
                if touches_top:
                    est_cy = blob_cy + 20
                elif touches_bottom:
                    est_cy = blob_cy - 20
                
                est_cx = max(50, min(img.width()-50, est_cx))
                est_cy = max(30, min(img.height()-30, est_cy))
                
                edge_rects.append({
                    'center': (est_cx, est_cy),
                    'confidence': 0.6,
                    'type': 'edge_based'
                })
        
        return edge_rects
    except:
        return []

# 11. 时钟和状态变量
clock = time.clock()
stable_frame_count = 0

print("高精度矩形追踪系统初始化完成")
print("PID参数: X轴(Kp=" + str(PID_X_KP) + ", Ki=" + str(max(PID_X_KI, 0.25)) + "), Y轴(Kp=" + str(PID_Y_KP) + ", Ki=" + str(max(PID_Y_KI, 0.22)) + ")")
print("优化特性: 稳态精度增强、自适应死区")

# 12. 主循环
while True:
    clock.tick()
    img = sensor.snapshot()
    
    # 1. 矩形检测
    rects = cv_lite.grayscale_find_rectangles_with_corners(
        img.to_grayscale(),
        threshold=(0, 100),
        area_threshold=100,
        pixels_threshold=100,
        approx_epsilon=0.04
    )
    
    # 2. 筛选最佳矩形
    best_detection = None
    smallest_rect_corners = None
    
    # 2.1 完整矩形检测
    min_area = float('inf')
    for rect in rects:
        x, y, w, h = rect[0], rect[1], rect[2], rect[3]
        corners = [
            (rect[4], rect[5]), (rect[6], rect[7]),
            (rect[8], rect[9]), (rect[10], rect[11])
        ]

        if is_valid_rect(corners):
            area = w * h
            if area < min_area:
                min_area = area
                smallest_rect_corners = corners
                best_detection = {
                    'center': calculate_center(corners),
                    'confidence': 1.0,
                    'type': 'complete'
                }

    # 2.2 边缘矩形检测
    if not best_detection:
        edge_rects = detect_edge_rectangles(img)
        if edge_rects:
            best_detection = edge_rects[0]
            center = best_detection['center']
            vw, vh = 50, 40
            smallest_rect_corners = [
                (center[0] - vw/2, center[1] - vh/2),
                (center[0] + vw/2, center[1] - vh/2),
                (center[0] + vw/2, center[1] + vh/2),
                (center[0] - vw/2, center[1] + vh/2)
            ]

    # 3. 高精度追踪控制
    if best_detection and smallest_rect_corners:
        center = best_detection['center']
        cx, cy = center
        
        stable_center = target_stabilizer.update((cx, cy))
        
        if stable_center and target_stabilizer.is_stable():
            stable_cx, stable_cy = stable_center
            
            image_center_x = sensor.width() / 2
            image_center_y = sensor.height() / 2
            
            error_x = stable_cx - image_center_x
            error_y = stable_cy - image_center_y
            
            pan_cmd = pid_x.compute(error_x)
            tilt_cmd = pid_y.compute(error_y)
            
            pid_x_status = pid_x.get_status()
            pid_y_status = pid_y.get_status()
            
            precision_mode = (target_stabilizer.is_high_precision() and 
                            pid_x_status['precision_mode'] and 
                            pid_y_status['precision_mode'])
            
            gimbal_controller.send_command(pan_cmd, tilt_cmd, int(stable_cx), int(stable_cy), precision_mode)
            
            if abs(error_x) < 3 and abs(error_y) < 3:
                stable_frame_count += 1
            else:
                stable_frame_count = 0
            
            center_color = (0, 255, 255) if precision_mode else (255, 255, 0)
            img.draw_circle(int(stable_cx), int(stable_cy), 8, color=center_color, thickness=2)
            
        else:
            stable_frame_count = 0
            if not target_stabilizer.is_stable():
                pid_x.reset()
                pid_y.reset()

        # 绘制检测结果
        if best_detection['type'] == 'complete':
            border_color = (255, 0, 0)
            corner_color = (0, 255, 0)
        else:
            border_color = (255, 165, 0)
            corner_color = (255, 255, 0)
            
        for i in range(4):
            x1, y1 = smallest_rect_corners[i]
            x2, y2 = smallest_rect_corners[(i+1) % 4]
            img.draw_line(int(x1), int(y1), int(x2), int(y2), color=border_color, thickness=2)
        
        for p in smallest_rect_corners:
            img.draw_circle(int(p[0]), int(p[1]), 5, color=corner_color, thickness=2)
    else:
        stable_frame_count = 0
        target_stabilizer.update(None)

    # 4. 显示信息
    fps = clock.fps()
    img.draw_string_advanced(10, 10, 16, "FPS: " + str(round(fps, 1)), color=(255, 255, 255))
    
    if target_stabilizer.stable_center:
        if target_stabilizer.is_high_precision():
            status_text = "HIGH-PRECISION"
            status_color = (0, 255, 255)
        elif target_stabilizer.is_stable():
            status_text = "TRACKING"
            status_color = (0, 255, 0)
        else:
            status_text = "UNSTABLE"
            status_color = (255, 255, 0)
        img.draw_string_advanced(10, 30, 16, "Status: " + status_text, color=status_color)
        
        cx, cy = target_stabilizer.stable_center
        img.draw_string_advanced(10, 50, 16, "Target: (" + str(round(cx, 1)) + "," + str(round(cy, 1)) + ")", color=(255, 255, 255))
        img.draw_string_advanced(10, 70, 16, "Stable: " + str(stable_frame_count), color=(255, 255, 255))
    else:
        img.draw_string_advanced(10, 30, 16, "Status: NO TARGET", color=(255, 0, 0))
    
    img.draw_string_advanced(10, 90, 16, "Rects: " + str(len(rects)), color=(255, 255, 255))
    
    if best_detection:
        detection_type = best_detection['type']
        type_color = (0, 255, 0) if detection_type == 'complete' else (255, 165, 0)
        img.draw_string_advanced(10, 110, 16, "Type: " + detection_type, color=type_color)

    Display.show_image(img,
                      x=round((lcd_width-sensor.width())/2),
                      y=round((lcd_height-sensor.height())/2))

    if int(fps * 10) % 20 == 0:
        target_info = "Target: " + str(target_stabilizer.stable_center) if target_stabilizer.stable_center else "No target"
        precision_info = " [HIGH-PRECISION]" if target_stabilizer.is_high_precision() else ""
        print("FPS: " + str(round(fps, 1)) + ", " + target_info + precision_info + ", Stable: " + str(stable_frame_count))
